<template>
  <div class="privacy-modal" @click.self="$emit('close')">
    <div class="privacy-content">
      <div class="privacy-header">
        <h2 class="privacy-title">隐私政策</h2>
        <button @click="$emit('close')" class="close-button" aria-label="关闭">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="privacy-body">
        <div class="privacy-section">
          <h3>引言</h3>
          <p>我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。</p>
        </div>

        <div class="privacy-section">
          <h3>1. 我们收集的信息</h3>
          <h4>1.1 您主动提供的信息</h4>
          <ul>
            <li><strong>注册信息：</strong>姓名、性别、年龄、学历、联系电话、身份证号等</li>
            <li><strong>测试信息：</strong>惯用手、色盲情况等个人特征信息</li>
            <li><strong>反馈信息：</strong>您通过客服或反馈功能提供的信息</li>
          </ul>
          
          <h4>1.2 我们自动收集的信息</h4>
          <ul>
            <li><strong>测试数据：</strong>测试过程中的反应时间、准确率、操作轨迹等</li>
            <li><strong>设备信息：</strong>设备型号、操作系统、浏览器类型等</li>
            <li><strong>日志信息：</strong>访问时间、IP地址、访问页面等</li>
            <li><strong>位置信息：</strong>基于IP地址的大致地理位置（如需要）</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>2. 信息使用目的</h3>
          <p>我们收集和使用您的个人信息，主要用于以下目的：</p>
          <ul>
            <li>提供认知测试服务和生成个性化报告</li>
            <li>验证用户身份，保障账户安全</li>
            <li>改进和优化我们的服务质量</li>
            <li>进行数据分析和科学研究（匿名化处理）</li>
            <li>遵守法律法规要求</li>
            <li>与您沟通服务相关事宜</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>3. 信息共享与披露</h3>
          <p>我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：</p>
          <ul>
            <li>获得您的明确同意</li>
            <li>法律法规要求或政府部门要求</li>
            <li>为保护我们或他人的合法权益</li>
            <li>与合作伙伴共享匿名化的统计数据</li>
            <li>在企业重组、合并或收购时转移数据</li>
          </ul>
          
          <p><strong>合作伙伴：</strong>我们可能与以下类型的合作伙伴共享信息：</p>
          <ul>
            <li>技术服务提供商（云存储、数据分析等）</li>
            <li>医疗机构或研究机构（匿名化数据）</li>
            <li>法律顾问或审计机构</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>4. 信息存储与安全</h3>
          <h4>4.1 存储地点</h4>
          <p>您的个人信息将存储在中华人民共和国境内。如需跨境传输，我们将严格按照法律法规要求执行。</p>
          
          <h4>4.2 存储期限</h4>
          <ul>
            <li>账户信息：账户存续期间</li>
            <li>测试数据：5年（用于科学研究）</li>
            <li>日志信息：6个月</li>
            <li>其他信息：根据业务需要和法律要求确定</li>
          </ul>
          
          <h4>4.3 安全措施</h4>
          <ul>
            <li>数据加密传输和存储</li>
            <li>访问权限控制和身份认证</li>
            <li>定期安全审计和漏洞扫描</li>
            <li>员工安全培训和保密协议</li>
            <li>数据备份和灾难恢复机制</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>5. 您的权利</h3>
          <p>根据相关法律法规，您享有以下权利：</p>
          <ul>
            <li><strong>知情权：</strong>了解我们处理您个人信息的情况</li>
            <li><strong>访问权：</strong>查看我们收集的您的个人信息</li>
            <li><strong>更正权：</strong>更正不准确或不完整的个人信息</li>
            <li><strong>删除权：</strong>要求删除您的个人信息</li>
            <li><strong>限制处理权：</strong>限制我们处理您的个人信息</li>
            <li><strong>数据可携权：</strong>获取您的个人信息副本</li>
            <li><strong>撤回同意权：</strong>撤回您之前给予的同意</li>
          </ul>
          
          <p>如需行使上述权利，请通过以下方式联系我们：</p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>电话：400-123-4567</li>
            <li>地址：[公司地址]</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>6. 未成年人保护</h3>
          <p>我们非常重视未成年人的个人信息保护：</p>
          <ul>
            <li>不满14周岁的儿童使用我们的服务需要监护人同意</li>
            <li>我们不会主动收集未成年人的个人信息</li>
            <li>如发现收集了未成年人信息，我们将及时删除</li>
            <li>监护人有权查看、更正或删除未成年人的信息</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>7. Cookie和类似技术</h3>
          <p>我们使用Cookie和类似技术来：</p>
          <ul>
            <li>记住您的登录状态和偏好设置</li>
            <li>分析网站使用情况，改进用户体验</li>
            <li>提供个性化的服务内容</li>
            <li>防范安全风险</li>
          </ul>
          
          <p>您可以通过浏览器设置管理Cookie，但这可能影响某些功能的使用。</p>
        </div>

        <div class="privacy-section">
          <h3>8. 隐私政策更新</h3>
          <p>我们可能会不时更新本隐私政策。更新后的政策将在网站上公布，并通过适当方式通知您。如果更新涉及重大变更，我们将征求您的同意。</p>
          
          <p>重大变更包括但不限于：</p>
          <ul>
            <li>服务模式发生重大变化</li>
            <li>个人信息使用目的发生重大变化</li>
            <li>个人信息共享对象发生变化</li>
            <li>您参与个人信息处理方面的权利发生重大变化</li>
          </ul>
        </div>

        <div class="privacy-section">
          <h3>9. 联系我们</h3>
          <p>如果您对本隐私政策有任何疑问、意见或建议，请通过以下方式联系我们：</p>
          <ul>
            <li><strong>邮箱：</strong><EMAIL></li>
            <li><strong>电话：</strong>400-123-4567</li>
            <li><strong>地址：</strong>[公司地址]</li>
            <li><strong>邮编：</strong>[邮政编码]</li>
          </ul>
          
          <p>我们将在收到您的反馈后15个工作日内回复。</p>
        </div>


      </div>
      

    </div>
  </div>
</template>

<script setup lang="ts">
defineEmits<{
  close: []
  agree: []
}>()
</script>

<style lang="scss" scoped>
.privacy-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.privacy-content {
  background: var(--card);
  border-radius: 16px;
  box-shadow: var(--shadow-xl);
  max-width: 800px;
  max-height: 90vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.privacy-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;
  border-bottom: 1px solid var(--border);
  margin-bottom: 24px;
}

.privacy-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--foreground);
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;

  &:hover {
    background: var(--muted);
    color: var(--foreground);
  }
}

.privacy-body {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--muted);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 3px;
  }
}

.privacy-section {
  margin-bottom: 24px;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 12px;
  }
  
  h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--foreground);
    margin: 16px 0 8px 0;
  }
  
  p {
    color: var(--muted-foreground);
    line-height: 1.6;
    margin-bottom: 8px;
  }
  
  ul {
    color: var(--muted-foreground);
    line-height: 1.6;
    padding-left: 20px;
    margin-bottom: 12px;
    
    li {
      margin-bottom: 4px;
      
      strong {
        color: var(--foreground);
      }
    }
  }
}

.privacy-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border);
  text-align: center;
  
  p {
    color: var(--muted-foreground);
    font-size: 14px;
    margin-bottom: 8px;
  }
}

.privacy-actions {
  display: flex;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--border);
  justify-content: flex-end;
}

.btn-secondary {
  background: var(--muted);
  color: var(--muted-foreground);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--accent);
    color: var(--foreground);
  }
}

.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: oklch(from var(--primary) calc(l - 0.05) c h);
  }
}

@media (max-width: 768px) {
  .privacy-modal {
    padding: 10px;
  }
  
  .privacy-content {
    max-height: 95vh;
  }
  
  .privacy-header,
  .privacy-body,
  .privacy-actions {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .privacy-title {
    font-size: 20px;
  }
  
  .privacy-actions {
    flex-direction: column;
    
    .btn-secondary,
    .btn-primary {
      width: 100%;
    }
  }
}
</style>
