<template>
  <div class="register-container">
    <!-- 顶部导航 -->
    <header class="register-header">
      <router-link to="/login" class="back-button" aria-label="返回登录">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
        </svg>
      </router-link>
      <h1 class="page-title">用户注册</h1>
    </header>

    <main class="register-content">
      <div class="register-card">

      <!-- 注册表单 -->
      <form @submit.prevent="handleRegister" class="register-form">
        <!-- 统一表单区域 -->
        <div class="form-section">
          <!-- 第一行：姓名、性别、学历 -->
          <div class="form-row-three">
            <div class="form-group">
              <label for="name" class="label required">姓名</label>
              <input
                id="name"
                v-model="registerForm.name"
                type="text"
                placeholder="请输入真实姓名"
                class="input"
                required
                maxlength="20"
              />
            </div>

            <div class="form-group">
              <label for="gender" class="label required">性别</label>
              <CustomSelect
                v-model="registerForm.gender"
                :options="genderOptions"
                placeholder="请选择性别"
              />
            </div>

            <div class="form-group">
              <label for="education" class="label required">学历</label>
              <CustomSelect
                v-model="registerForm.education"
                :options="educationOptions"
                placeholder="请选择学历"
              />
            </div>
          </div>

          <!-- 第二行：联系电话、身份证号、惯用手 -->
          <div class="form-row-three">
            <div class="form-group">
              <label for="contactPhone" class="label required">联系电话</label>
              <input
                id="contactPhone"
                v-model="registerForm.contactPhone"
                type="tel"
                placeholder="请输入联系电话"
                class="input"
                required
                pattern="[0-9]{11}"
              />
            </div>

            <div class="form-group">
              <label for="idCard" class="label required">身份证号</label>
              <input
                id="idCard"
                v-model="registerForm.idCard"
                type="text"
                class="input"
                :class="{ 'input-error': registerForm.idCard && !isValidIdCard(registerForm.idCard) }"
                placeholder="请输入18位身份证号"
                pattern="[0-9X]{18}"
                maxlength="18"
                required
              />
              <div
                v-if="registerForm.idCard && !isValidIdCard(registerForm.idCard)"
                class="error-message"
              >
                请输入正确的18位身份证号格式
              </div>
            </div>

            <div class="form-group">
              <label for="dominantHand" class="label required">惯用手</label>
              <CustomSelect
                v-model="registerForm.dominantHand"
                :options="dominantHandOptions"
                placeholder="请选择惯用手"
              />
            </div>
          </div>

          <!-- 第三行：是否色盲 -->
          <div class="form-row-three">
            <div class="form-group">
              <fieldset class="radio-group">
                <legend class="label required">是否色盲</legend>
                <div class="radio-options">
                  <label class="radio-label">
                    <input
                      type="radio"
                      v-model="registerForm.isColorBlind"
                      :value="false"
                      name="colorBlind"
                      class="radio"
                      required
                    />
                    <span class="radio-text">否</span>
                  </label>
                  <label class="radio-label">
                    <input
                      type="radio"
                      v-model="registerForm.isColorBlind"
                      :value="true"
                      name="colorBlind"
                      class="radio"
                      required
                    />
                    <span class="radio-text">是</span>
                  </label>
                </div>
              </fieldset>
            </div>
            <!-- 空白占位 -->
            <div class="form-group"></div>
            <div class="form-group"></div>
          </div>
        </div>

        <!-- 用户协议 -->
        <div class="form-section">
          <label class="checkbox-label agreement-label">
            <input
              type="checkbox"
              v-model="agreementAccepted"
              required
              class="checkbox"
            />
            <span class="checkbox-text">
              我已阅读并同意
              <button type="button" class="link-button" @click="showAgreement">
                《用户协议》
              </button>
              和
              <button type="button" class="link-button" @click="showPrivacy">
                《隐私政策》
              </button>
            </span>
          </label>
        </div>

        <!-- 提交按钮 -->
        <button
          type="submit"
          :disabled="isLoading || !isFormValid"
          class="btn btn-primary register-button"
          :aria-busy="isLoading"
        >
          <span v-if="isLoading" class="loading-spinner" aria-hidden="true"></span>
          {{ isLoading ? '注册中...' : '立即注册' }}
        </button>
      </form>

      <!-- 成功提示 -->
      <div
        v-if="successMessage"
        role="alert"
        class="success-message"
        aria-live="polite"
      >
        <div class="success-content">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="success-icon">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
          <div>
            <div class="success-title">注册成功！</div>
            <div class="success-subtitle">您的患者编号：{{ patientNumber }}</div>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <div
        v-if="errorMessage"
        role="alert"
        class="error-message"
        aria-live="polite"
      >
        {{ errorMessage }}
      </div>
      </div>
    </main>

    <!-- 用户协议弹窗 -->
    <UserAgreement
      v-if="showUserAgreement"
      @close="showUserAgreement = false"
      @agree="handleAgreementAccept"
    />

    <!-- 隐私政策弹窗 -->
    <PrivacyPolicy
      v-if="showPrivacyPolicy"
      @close="showPrivacyPolicy = false"
      @agree="handlePrivacyAccept"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { educationOptions } from '@/utils/mockData'
import type { RegisterData } from '@/types/user'
import CustomSelect from '@/components/CustomSelect.vue'
import UserAgreement from '@/components/UserAgreement.vue'
import PrivacyPolicy from '@/components/PrivacyPolicy.vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref(false)
const patientNumber = ref('')
const agreementAccepted = ref(false)
const showUserAgreement = ref(false)
const showPrivacyPolicy = ref(false)

// 选项数据
const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
]

const dominantHandOptions = [
  { label: '右手', value: 'right' },
  { label: '左手', value: 'left' }
]

const registerForm = ref<RegisterData>({
  name: '',
  education: '',
  gender: 'male' as 'male' | 'female' | 'other',
  contactPhone: '',
  dominantHand: 'right' as 'right' | 'left',
  isColorBlind: false,
  idCard: ''
})

// 身份证号验证函数
const isValidIdCard = (idCard: string | undefined): boolean => {
  if (!idCard) return false
  // 简单的18位身份证号格式验证
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return idCardRegex.test(idCard)
}

// 计算属性
const isFormValid = computed(() => {
  return registerForm.value.name &&
         registerForm.value.education &&
         registerForm.value.gender &&
         registerForm.value.contactPhone &&
         registerForm.value.dominantHand &&
         isValidIdCard(registerForm.value.idCard) &&
         (registerForm.value.isColorBlind === true || registerForm.value.isColorBlind === false) &&
         agreementAccepted.value
})

// 处理注册
const handleRegister = async () => {
  if (!isFormValid.value) {
    errorMessage.value = '请填写所有必填项并同意用户协议'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const result = await authStore.register(registerForm.value)
    
    if (result.success) {
      successMessage.value = true
      patientNumber.value = result.patientNumber || ''
      
      // 3秒后跳转到首页
      setTimeout(() => {
        router.push('/home')
      }, 3000)
    } else {
      errorMessage.value = result.message
    }
  } catch (error) {
    errorMessage.value = '注册失败，请稍后重试'
    console.error('Register error:', error)
  } finally {
    isLoading.value = false
  }
}

// 显示用户协议
const showAgreement = () => {
  showUserAgreement.value = true
}

// 显示隐私政策
const showPrivacy = () => {
  showPrivacyPolicy.value = true
}

// 处理用户协议同意
const handleAgreementAccept = () => {
  agreementAccepted.value = true
  showUserAgreement.value = false
}

// 处理隐私政策同意
const handlePrivacyAccept = () => {
  showPrivacyPolicy.value = false
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: var(--background);
}

// 顶部导航样式 - 参考个人中心页面
.register-header {
  background: var(--card);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 24px;
  position: relative;
  min-height: 60px;
}

.back-button {
  position: absolute;
  left: 24px;
  background: var(--card);
  border: 1px solid var(--border);
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;

  &:hover {
    color: var(--foreground);
    background: var(--muted);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.page-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
}

// 主内容区域
.register-content {
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 60px);
}

.register-card {
  background: transparent;
  padding: $spacing-xl;
  width: 100%;
  max-width: 1280px;
  max-height: 95vh;
  overflow-y: auto;
  animation: fadeIn 0.5s ease-out;
  color: var(--foreground);
}



.register-form {
  margin-bottom: $spacing-md;
}

.form-section {
  margin-bottom: $spacing-lg;

  &:last-of-type {
    margin-bottom: $spacing-md;
  }
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: oklch(0.3 0.02 240);
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-xs;
  border-bottom: 2px solid oklch(0.85 0.02 220);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--primary);
    border-radius: 1px;
  }
}

.form-group {
  margin-bottom: $spacing-md;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;

  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

.form-row-three {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;

  @media (max-width: $tablet) {
    grid-template-columns: 1fr 1fr;

    .form-group:nth-child(3) {
      grid-column: 1 / -1;
    }
  }

  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

.label {
  &.required::after {
    content: ' *';
    color: var(--color-destructive);
  }
}

// 输入框透明背景
.register-card {
  input.input,
  select.input,
  textarea.input,
  .input {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: 1px solid oklch(0.88 0.005 240);
    border-radius: 12px;
    padding: 12px 16px;
    font-size: $font-size-base;
    color: var(--foreground);
    transition: all 0.3s ease;
    width: 100%;
    min-height: 48px;

    &:focus {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
      border-color: oklch(0.65 0.15 220);
      box-shadow: 0 0 0 2px oklch(0.65 0.15 220 / 0.2);
      outline: none;
    }

    &:hover {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
      border-color: oklch(0.8 0.01 240);
    }

    &.input-error {
      border-color: var(--destructive) !important;
      box-shadow: 0 0 0 2px oklch(from var(--destructive) l c h / 0.1);
    }
  }

  // 确保所有表单控件高度一致
  .form-group {
    .input,
    .select-trigger {
      min-height: 48px;
      padding: 12px 16px;
      border: 1px solid oklch(0.88 0.005 240);
      border-radius: 12px;
      font-size: $font-size-base;
      line-height: 1.5;
      box-sizing: border-box;
    }
  }


}

.checkbox-group,
.radio-group {
  border: none;
  margin: 0;
  padding: 0;
}

.radio-options {
  display: flex;
  gap: $spacing-lg;
  margin-top: $spacing-xs;
}

.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: $font-size-sm;

  .checkbox,
  .radio {
    margin-right: $spacing-sm;
    width: 16px;
    height: 16px;
  }
}

.agreement-label {
  #background: var(--muted);
  backdrop-filter: blur(5px);
  #border: 1px solid var(--border);
  padding: $spacing-sm;
  border-radius: 12px;
  margin-bottom: $spacing-md;
  color: var(--foreground);

  .link-button {
    background: none;
    border: none;
    color: var(--primary);
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;

    &:hover {
      color: var(--accent);
    }
  }
}

.register-button {
  width: 100%;
  background: var(--primary) !important;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-weight: 600;
  font-size: $font-size-base;
  color: var(--primary-foreground);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  margin-top: $spacing-sm;

  &:hover:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.05) c h) !important;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  &:active:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.1) c h) !important;
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: $spacing-sm;
  }
}

.success-message {
  background: oklch(from var(--color-primary) l c h / 0.1);
  color: var(--color-primary);
  padding: $spacing-lg;
  border-radius: var(--radius);
  border: 1px solid oklch(from var(--color-primary) l c h / 0.2);
  margin-bottom: $spacing-lg;
}

.success-content {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.success-icon {
  flex-shrink: 0;
}

.success-title {
  font-weight: 600;
  margin-bottom: $spacing-xs;
}

.success-subtitle {
  font-size: $font-size-sm;
  opacity: 0.8;
}

.error-message {
  background: oklch(from var(--color-destructive) l c h / 0.1);
  color: var(--color-destructive);
  padding: $spacing-md;
  border-radius: var(--radius);
  border: 1px solid oklch(from var(--color-destructive) l c h / 0.2);
  text-align: center;
  font-size: $font-size-sm;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 平板端表单优化
@media (min-width: $mobile) and (max-width: $tablet) {
  .register-card {
    .form-group {
      margin-bottom: $spacing-xl;
    }

    .form-row {
      gap: $spacing-xl;

      .form-group {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .register-header {
    padding: 12px 16px;
    min-height: 56px;
  }

  .back-button {
    left: 16px;
    min-height: 40px;
    min-width: 40px;
    padding: 6px;
  }

  .page-title {
    font-size: $font-size-base;
  }

  .register-content {
    padding: $spacing-md;
    min-height: calc(100vh - 56px);
  }

  .register-card {
    padding: $spacing-xl;
    max-height: 95vh;
  }
}
</style>