<template>
  <div class="assessment-container">
    <!-- 头部区域 -->
    <div class="header-section">
      <button @click="goBack" class="back-button" aria-label="返回首页">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <!-- 全局计时器 - 标题左侧 -->
      <GlobalTimer v-if="isRunning" position="title-left" size="medium" />

      <h1 class="page-title">认知能力综合评估</h1>

      <div class="header-right">
        <ThemeToggle />
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="content-section">
      <!-- 评估介绍 -->
      <div class="intro-card">
        <div class="intro-header">
          <div class="intro-icon">🧠</div>
          <div class="intro-text">
            <h2 class="intro-title">综合认知能力评估</h2>
            <p class="intro-subtitle">全面评估您的认知功能状态</p>
          </div>
        </div>
        
        <div class="intro-description">
          <p>本次评估包含多个认知测试项目，将全面评估您的记忆力、注意力、反应速度、认知灵活性等多个维度的认知功能。</p>
          <p>整个评估过程大约需要 <strong>20-30分钟</strong>，请确保在安静的环境中完成测试。</p>
        </div>
      </div>

      <!-- 测试项目列表 -->
      <div class="test-items-section">
        <h3 class="section-title">测试项目</h3>
        <div class="test-items-grid">
          <div 
            v-for="(item, index) in testItems" 
            :key="item.id"
            class="test-item-card"
            :class="{ 'completed': item.completed, 'current': item.current }"
          >
            <div class="item-number">{{ index + 1 }}</div>
            <div class="item-content">
              <h4 class="item-title">{{ item.name }}</h4>
              <p class="item-description">{{ item.description }}</p>
              <div class="item-meta">
                <span class="item-duration">⏱️ {{ item.duration }}</span>
                <span class="item-difficulty">📊 {{ item.difficulty }}</span>
              </div>
            </div>
            <div class="item-status">
              <div v-if="item.completed" class="status-completed">✅</div>
              <div v-else-if="item.current" class="status-current">▶️</div>
              <div v-else class="status-pending">⏳</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 重要提示 -->
      <div class="notice-card">
        <div class="notice-header">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 9v4M12 17h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <h4>重要提示</h4>
        </div>
        <ul class="notice-list">
          <li>请确保在安静、不受干扰的环境中进行测试</li>
          <li>测试过程中将自动计时，请按照指导语认真完成</li>
          <li>每个测试项目都有详细的指导说明，请仔细阅读</li>
          <li>如有疑问，可以随时联系工作人员</li>
          <li>测试结果将用于科学研究，请如实作答</li>
        </ul>
      </div>

      <!-- 开始按钮 -->
      <div class="action-section">
        <button 
          @click="startAssessment" 
          class="start-button"
          :disabled="isStarting"
        >
          <div v-if="isStarting" class="loading-spinner"></div>
          <span>{{ isStarting ? '正在启动...' : '开始认知评估' }}</span>
        </button>
        
        <p class="action-note">
          点击开始后，系统将开始计时，请做好准备
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { GlobalTimerService, useGlobalTimer } from '@/utils/globalTimer'
import ThemeToggle from '@/components/ThemeToggle.vue'
import GlobalTimer from '@/components/GlobalTimer.vue'

const router = useRouter()
const { isRunning } = useGlobalTimer()

// 响应式数据
const isStarting = ref(false)

// 测试项目数据
const testItems = ref([
  {
    id: 'pdq5',
    name: 'PDQ-5 认知评估',
    description: '评估记忆力、注意力和认知灵活性',
    duration: '10-15分钟',
    difficulty: '中等',
    completed: false,
    current: true
  },
  {
    id: 'stroop',
    name: 'Stroop 色词测试',
    description: '评估注意力和认知控制能力',
    duration: '5-8分钟',
    difficulty: '中等',
    completed: false,
    current: false
  },
  {
    id: 'nback',
    name: 'N-back 工作记忆测试',
    description: '评估工作记忆和注意力持续性',
    duration: '8-12分钟',
    difficulty: '较难',
    completed: false,
    current: false
  }
])

// 返回首页
const goBack = () => {
  router.push('/home')
}

// 开始评估
const startAssessment = () => {
  isStarting.value = true
  
  // 启动全局计时器
  GlobalTimerService.start('认知能力综合评估')
  
  // 模拟启动延迟
  setTimeout(() => {
    // 跳转到第一个测试项目
    router.push('/tests/pdq5/instructions')
  }, 1000)
}

// 组件挂载时检查是否已有计时器在运行
onMounted(() => {
  // 如果全局计时器已经在运行，说明用户可能是从测试中返回的
  if (GlobalTimerService.isRunning()) {
    console.log('检测到正在进行的评估任务:', GlobalTimerService.getCurrentTask())
  }
})
</script>

<style lang="scss" scoped>
.assessment-container {
  min-height: 100vh;
  background: var(--background);
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-xl;
  position: relative;
  padding-left: 200px; // 为计时器留出空间
}

.back-button {
  position: absolute;
  left: 0;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-sm;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--muted);
    color: var(--foreground);
    transform: translateX(-2px);
  }
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--foreground);
  text-align: center;
  width: 100%;
  margin: 0;
}

.header-right {
  position: absolute;
  right: 0;
}

.content-section {
  flex: 1;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.intro-card {
  background: var(--card);
  border-radius: 16px;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

.intro-header {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
}

.intro-icon {
  font-size: 48px;
  line-height: 1;
}

.intro-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-xs 0;
}

.intro-subtitle {
  color: var(--muted-foreground);
  margin: 0;
}

.intro-description {
  color: var(--foreground);
  line-height: 1.6;

  p {
    margin: 0 0 $spacing-sm 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  strong {
    color: var(--primary);
    font-weight: 600;
  }
}

.test-items-section {
  margin-bottom: $spacing-lg;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-md 0;
}

.test-items-grid {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.test-item-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  transition: all 0.3s ease;

  &.current {
    border-color: var(--primary);
    background: var(--primary-foreground);
  }

  &.completed {
    opacity: 0.7;
  }
}

.item-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--muted);
  color: var(--muted-foreground);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;

  .current & {
    background: var(--primary);
    color: var(--primary-foreground);
  }
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: $font-size-base;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-xs 0;
}

.item-description {
  color: var(--muted-foreground);
  margin: 0 0 $spacing-xs 0;
  font-size: $font-size-sm;
}

.item-meta {
  display: flex;
  gap: $spacing-md;
  font-size: $font-size-xs;
  color: var(--muted-foreground);
}

.item-status {
  font-size: 20px;
  flex-shrink: 0;
}

.notice-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
  color: var(--primary);

  h4 {
    margin: 0;
    font-weight: 600;
  }
}

.notice-list {
  margin: 0;
  padding-left: $spacing-lg;
  color: var(--muted-foreground);

  li {
    margin-bottom: $spacing-xs;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.action-section {
  text-align: center;
  padding: $spacing-lg 0;
}

.start-button {
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: 12px;
  padding: $spacing-md $spacing-xl;
  font-size: $font-size-lg;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: $spacing-sm;
  min-width: 200px;
  justify-content: center;

  &:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.action-note {
  margin: $spacing-md 0 0 0;
  color: var(--muted-foreground);
  font-size: $font-size-sm;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .assessment-container {
    padding: $spacing-sm;
  }

  .header-section {
    padding-left: $spacing-sm; // 移动端减少左侧padding
  }

  .intro-card,
  .notice-card {
    padding: $spacing-md;
  }

  .test-item-card {
    padding: $spacing-md;
  }

  .page-title {
    font-size: $font-size-xl;
  }

  .intro-header {
    flex-direction: column;
    text-align: center;
    gap: $spacing-sm;
  }

  .item-meta {
    flex-direction: column;
    gap: $spacing-xs;
  }
}
</style>
