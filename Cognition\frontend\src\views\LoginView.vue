<template>
  <div class="login-container">
    <!-- 主标题 -->
    <div class="main-title">
      <h1>认知测试系统</h1>
    </div>

    <!-- 登录表单 -->
    <form @submit.prevent="handleLogin" class="login-form">
      <div class="input-group">
        <!-- 输入框 -->
        <input
          v-model="loginForm.value"
          :type="getInputType()"
          placeholder="请输入患者编号或手机号"
          class="login-input"
          required
          :aria-label="inputAriaLabel"
        />
        
        <!-- 登录按钮 -->
        <button
          type="submit"
          :disabled="isLoading || !loginForm.value"
          class="login-button"
          :aria-busy="isLoading"
        >
          <span v-if="isLoading" class="loading-spinner" aria-hidden="true"></span>
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
        
        <!-- 二维码登录按钮 -->
        <button
          type="button"
          @click="handleQRLogin"
          class="qr-button"
          aria-label="二维码登录"
          title="二维码登录"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM17 19h2v2h-2zM19 15h2v2h-2zM19 17h2v2h-2z"/>
          </svg>
        </button>
      </div>

      <!-- 注册链接 -->
      <div class="register-link">
        <span class="register-text">没有患者代码？</span>
        <router-link to="/register" class="register-btn">
          立即注册
        </router-link>
      </div>
    </form>

    <!-- 错误提示 -->
    <div
      v-if="errorMessage"
      role="alert"
      class="error-message"
      aria-live="polite"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { LoginCredentials } from '@/types/user'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const isLoading = ref(false)
const errorMessage = ref('')

const loginForm = ref({
  value: ''
})

// 计算属性
const inputAriaLabel = computed(() => '请输入患者编号或手机号进行登录')

// 自动检测输入类型
const detectInputType = (value: string) => {
  // 如果是11位数字，判断为手机号
  if (/^1[3-9]\d{9}$/.test(value)) {
    return 'phone'
  }
  // 如果以P开头，判断为患者编号
  if (/^P\d+/.test(value)) {
    return 'patientNumber'
  }
  // 默认按患者编号处理
  return 'patientNumber'
}

// 获取输入框类型
const getInputType = () => {
  const value = loginForm.value.value
  if (/^1[3-9]/.test(value)) {
    return 'tel'
  }
  return 'text'
}

// 处理登录
const handleLogin = async () => {
  const inputValue = loginForm.value.value.trim()
  if (!inputValue) {
    errorMessage.value = '请输入患者编号或手机号'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const loginType = detectInputType(inputValue)
    const credentials: LoginCredentials = {
      type: loginType,
      value: inputValue
    }

    const result = await authStore.login(credentials)
    
    if (result.success) {
      await router.push('/home')
    } else {
      errorMessage.value = result.message
    }
  } catch (error) {
    errorMessage.value = '登录失败，请稍后重试'
    console.error('Login error:', error)
  } finally {
    isLoading.value = false
  }
}

// 处理二维码登录
const handleQRLogin = () => {
  // 模拟二维码登录
  alert('二维码登录功能开发中...\n\n您可以使用以下测试账户：\n• 患者编号：P123456001\n• 手机号：13800138000')
}

// 生命周期
onMounted(() => {
  // 恢复登录状态
  authStore.restoreAuth()
  
  // 如果已登录，跳转到首页
  if (authStore.isLoggedIn) {
    router.push('/home')
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: url('/images/bg.png') center center/cover no-repeat, var(--background);
  padding: $spacing-2xl;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: oklch(from var(--background) l c h / 0.1);
    backdrop-filter: blur(2px);
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

.main-title {
  margin-bottom: 120px;

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--foreground);
    text-align: center;
    letter-spacing: 0.5px;
    margin: 0;
    text-shadow: 0 2px 8px oklch(from var(--background) l c h / 0.8);
    padding: $spacing-lg $spacing-xl;
    border-radius: 20px;
    backdrop-filter: blur(10px);
  }
}

.login-form {
  width: 100%;
  max-width: 1280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xl;
}

.input-group {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  width: 100%;
  max-width: 700px;
}

.login-input {
  flex: 1;
  height: 56px;
  padding: 0 $spacing-xl;
  border: 2px solid var(--border);
  border-radius: 28px;
  font-size: 1.1rem;
  background: oklch(from var(--card) l c h / 0.95);
  backdrop-filter: blur(15px);
  color: var(--foreground);
  transition: all 0.3s ease;
  outline: none;
  min-width: 350px;
  box-shadow: var(--shadow-lg);

  &::placeholder {
    color: var(--muted-foreground);
    font-weight: 400;
  }

  &:focus {
    border-color: var(--primary);
    background: oklch(from var(--card) l c h / 0.98);
    box-shadow: 0 0 0 4px oklch(from var(--primary) l c h / 0.15), var(--shadow-xl);
  }

  &:hover:not(:focus) {
    border-color: var(--accent);
    box-shadow: var(--shadow-xl);
  }
}

.login-button {
  height: 56px;
  padding: 0 32px;
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: 28px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
  min-width: 120px;
  box-shadow: 0 2px 8px oklch(from var(--primary) l c h / 0.3);

  &:hover:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.05) c h);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px oklch(from var(--primary) l c h / 0.4);
  }

  &:active:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px oklch(from var(--primary) l c h / 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: $spacing-sm;
  }
}

.qr-button {
  width: 56px;
  height: 56px;
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: none;
  border-radius: 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &:hover {
    background: oklch(from var(--secondary) calc(l - 0.05) c h);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px oklch(from var(--secondary) l c h / 0.3);
  }

  &:active {
    background: oklch(from var(--secondary) calc(l - 0.1) c h);
    transform: translateY(0);
  }

  svg {
    width: 24px;
    height: 24px;
  }
}

.register-link {
  text-align: center;
  margin-top: $spacing-lg;
  #background: rgba(255, 255, 255, 0.9);
  padding: $spacing-md $spacing-lg;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  #border: 1px solid rgba(255, 255, 255, 0.3);
}

.register-text {
  color: var(--muted-foreground);
  font-size: 0.95rem;
  margin-right: $spacing-sm;
}

.register-btn {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.2s ease;

  &:hover {
    color: var(--accent);
    text-decoration: underline;
  }
}

.error-message {
  background: oklch(from var(--destructive) l c h / 0.95);
  color: var(--destructive-foreground);
  padding: $spacing-md $spacing-lg;
  border-radius: 12px;
  border: 1px solid var(--destructive);
  text-align: center;
  font-size: 0.9rem;
  max-width: 400px;
  margin-top: $spacing-lg;
  animation: slideIn 0.3s ease-out;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px oklch(from var(--destructive) l c h / 0.3);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: $tablet) {
  .login-container {
    padding: $spacing-xl $spacing-lg;
  }
  
  .main-title {
    margin-bottom: 80px;
    
    h1 {
      font-size: 2rem;
    }
  }
  
  .input-group {
    max-width: 600px;
  }
  
  .login-input {
    min-width: 300px;
  }
}

@media (max-width: $mobile) {
  .login-container {
    padding: $spacing-lg $spacing-md;
  }
  
  .main-title {
    margin-bottom: 60px;
    
    h1 {
      font-size: 1.75rem;
    }
  }
  
  .input-group {
    flex-direction: column;
    gap: $spacing-md;
    
    .login-input {
      width: 100%;
      min-width: auto;
    }
    
    .login-button {
      width: 100%;
      min-width: auto;
    }
    
    .qr-button {
      align-self: center;
    }
  }
}

@media (max-width: 360px) {
  .main-title h1 {
    font-size: 1.5rem;
  }
  
  .login-input {
    height: 48px;
    font-size: 0.9rem;
  }
  
  .login-button,
  .qr-button {
    height: 48px;
  }
}
</style>