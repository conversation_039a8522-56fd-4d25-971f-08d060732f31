<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心页面优化演示</title>
    <style>
        :root {
            --background: oklch(0.9824 0.0013 286.3757);
            --foreground: oklch(0.3211 0 0);
            --card: oklch(1.0000 0 0);
            --card-foreground: oklch(0.3211 0 0);
            --muted: oklch(0.8828 0.0285 98.1033);
            --muted-foreground: oklch(0.5382 0 0);
            --border: oklch(0.8699 0 0);
            --primary: oklch(0.6487 0.1538 150.3071);
            --primary-foreground: oklch(1.0000 0 0);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
        }

        [data-theme="dark"] {
            --background: oklch(0.2303 0.0125 264.2926);
            --foreground: oklch(0.9219 0 0);
            --card: oklch(0.2697 0.0125 264.2926);
            --card-foreground: oklch(0.9219 0 0);
            --muted: oklch(0.3867 0 0);
            --muted-foreground: oklch(0.6382 0 0);
            --border: oklch(0.3867 0 0);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--background);
            color: var(--foreground);
            transition: all 0.3s ease;
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .demo-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--foreground);
        }

        .theme-toggle {
            background: var(--primary);
            color: var(--primary-foreground);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .demo-section {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--foreground);
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border);
        }

        .demo-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .label {
            font-size: 14px;
            font-weight: 500;
            color: var(--foreground);
        }

        .input {
            padding: 12px 16px;
            border: 1px solid var(--border);
            border-radius: 8px;
            background: transparent;
            color: var(--foreground);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px oklch(from var(--primary) l c h / 0.1);
        }

        .input.readonly {
            background: var(--muted);
            cursor: not-allowed;
        }

        .time-info {
            display: flex;
            gap: 24px;
            flex-wrap: wrap;
            padding: 16px;
            background: var(--muted);
            border-radius: 8px;
            margin-bottom: 16px;
        }

        .time-item {
            font-size: 14px;
            color: var(--muted-foreground);
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #22c55e;
        }

        .status-warning {
            background: #f59e0b;
        }

        .status-info {
            background: #3b82f6;
        }

        @media (max-width: 768px) {
            .demo-form {
                grid-template-columns: 1fr;
            }
            
            .demo-header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">个人中心页面优化演示</h1>
            <button class="theme-toggle" onclick="toggleTheme()">切换暗黑模式</button>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🔧 最新优化项目验证</h2>

            <div style="margin-bottom: 24px;">
                <h3 style="margin-bottom: 12px; color: var(--foreground);">1. 主题切换按钮对齐</h3>
                <div style="padding: 16px; background: var(--muted); border-radius: 8px;">
                    <span class="status-indicator status-success"></span>
                    主题切换按钮已与返回按钮水平对齐 (top: 16px)
                </div>
            </div>

            <div style="margin-bottom: 24px;">
                <h3 style="margin-bottom: 12px; color: var(--foreground);">2. 输入框和下拉框高度统一</h3>
                <div class="demo-form">
                    <div class="form-group">
                        <label class="label">文本输入框</label>
                        <input type="text" class="input" placeholder="48px高度" style="min-height: 48px;">
                    </div>

                    <div class="form-group">
                        <label class="label">下拉选择框</label>
                        <select class="input" style="min-height: 48px;">
                            <option>选项1</option>
                            <option>选项2</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="label">电话输入框</label>
                        <input type="tel" class="input" placeholder="48px高度" style="min-height: 48px;">
                    </div>
                </div>
                <div style="padding: 16px; background: var(--muted); border-radius: 8px; margin-top: 16px;">
                    <span class="status-indicator status-success"></span>
                    所有表单元素统一为48px最小高度，确保视觉一致性
                </div>
            </div>

        <div class="demo-section">
            <h2 class="section-title">✅ 之前的优化项目</h2>
            
            <div style="margin-bottom: 24px;">
                <h3 style="margin-bottom: 12px; color: var(--foreground);">1. 标题居中显示</h3>
                <div style="text-align: center; padding: 16px; background: var(--muted); border-radius: 8px;">
                    <span class="status-indicator status-success"></span>
                    <strong>个人信息</strong> - 页面标题已居中显示
                </div>
            </div>

            <div style="margin-bottom: 24px;">
                <h3 style="margin-bottom: 12px; color: var(--foreground);">2. 时间信息并排显示</h3>
                <div class="time-info">
                    <span class="time-item">
                        <span class="status-indicator status-success"></span>
                        注册时间：2024年01月15日 18:30
                    </span>
                    <span class="time-item">
                        <span class="status-indicator status-success"></span>
                        最后登录：2024年01月20日 22:22
                    </span>
                </div>
            </div>

            <div style="margin-bottom: 24px;">
                <h3 style="margin-bottom: 12px; color: var(--foreground);">3. 移除编辑按钮</h3>
                <div style="padding: 16px; background: var(--muted); border-radius: 8px;">
                    <span class="status-indicator status-success"></span>
                    右上角编辑按钮已移除，界面更简洁
                </div>
            </div>

            <div style="margin-bottom: 24px;">
                <h3 style="margin-bottom: 12px; color: var(--foreground);">4. 移除账户信息区域</h3>
                <div style="padding: 16px; background: var(--muted); border-radius: 8px;">
                    <span class="status-indicator status-success"></span>
                    独立的账户信息区域已移除，布局更紧凑
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🔧 身份证号字段测试</h2>
            
            <div class="demo-form">
                <div class="form-group">
                    <label class="label">身份证号 (有值)</label>
                    <input type="text" class="input readonly" value="110101199001011234" readonly>
                    <small style="color: var(--muted-foreground);">只读状态显示实际值</small>
                </div>
                
                <div class="form-group">
                    <label class="label">身份证号 (空值)</label>
                    <input type="text" class="input readonly" value="" readonly>
                    <small style="color: var(--muted-foreground);">只读状态为空，无placeholder</small>
                </div>
                
                <div class="form-group">
                    <label class="label">身份证号 (编辑模式)</label>
                    <input type="text" class="input" placeholder="请输入18位身份证号" maxlength="18">
                    <small style="color: var(--muted-foreground);">编辑模式显示placeholder</small>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🎨 暗黑模式测试</h2>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div style="padding: 16px; background: var(--muted); border-radius: 8px;">
                    <h4 style="margin-bottom: 8px; color: var(--foreground);">当前主题状态</h4>
                    <p style="color: var(--muted-foreground);" id="theme-status">亮色模式</p>
                </div>
                
                <div style="padding: 16px; background: var(--muted); border-radius: 8px;">
                    <h4 style="margin-bottom: 8px; color: var(--foreground);">CSS变量支持</h4>
                    <p style="color: var(--muted-foreground);">
                        <span class="status-indicator status-success"></span>
                        所有颜色使用CSS变量
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            body.setAttribute('data-theme', newTheme);
            
            const statusElement = document.getElementById('theme-status');
            statusElement.textContent = newTheme === 'dark' ? '暗黑模式' : '亮色模式';
            
            // 更新按钮文字
            const toggleButton = document.querySelector('.theme-toggle');
            toggleButton.textContent = newTheme === 'dark' ? '切换亮色模式' : '切换暗黑模式';
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('个人中心页面优化演示已加载');
        });
    </script>
</body>
</html>
