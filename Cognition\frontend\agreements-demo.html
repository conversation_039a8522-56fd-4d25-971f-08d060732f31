<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户协议和隐私政策演示</title>
    <style>
        :root {
            --background: oklch(0.9824 0.0013 286.3757);
            --foreground: oklch(0.3211 0 0);
            --card: oklch(1.0000 0 0);
            --muted: oklch(0.8828 0.0285 98.1033);
            --muted-foreground: oklch(0.5382 0 0);
            --border: oklch(0.8699 0 0);
            --primary: oklch(0.6487 0.1538 150.3071);
            --primary-foreground: oklch(1.0000 0 0);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        [data-theme="dark"] {
            --background: oklch(0.2303 0.0125 264.2926);
            --foreground: oklch(0.9219 0 0);
            --card: oklch(0.2697 0.0125 264.2926);
            --muted: oklch(0.3867 0 0);
            --muted-foreground: oklch(0.6382 0 0);
            --border: oklch(0.3867 0 0);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--background);
            color: var(--foreground);
            transition: all 0.3s ease;
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .demo-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--foreground);
        }

        .theme-toggle {
            background: var(--primary);
            color: var(--primary-foreground);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .demo-section {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--foreground);
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border);
        }

        .demo-buttons {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .demo-button {
            background: var(--primary);
            color: var(--primary-foreground);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .demo-button:hover {
            background: oklch(from var(--primary) calc(l - 0.05) c h);
            transform: translateY(-1px);
        }

        .demo-button.secondary {
            background: var(--muted);
            color: var(--muted-foreground);
        }

        .demo-button.secondary:hover {
            background: var(--border);
            color: var(--foreground);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #22c55e;
        }

        .info-box {
            background: var(--muted);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }

        .info-box h4 {
            color: var(--foreground);
            margin-bottom: 8px;
        }

        .info-box p {
            color: var(--muted-foreground);
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .info-box ul {
            color: var(--muted-foreground);
            padding-left: 20px;
        }

        .info-box li {
            margin-bottom: 4px;
        }

        @media (max-width: 768px) {
            .demo-header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
            
            .demo-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">用户协议和隐私政策演示</h1>
            <button class="theme-toggle" onclick="toggleTheme()">切换暗黑模式</button>
        </div>

        <div class="demo-section">
            <h2 class="section-title">📋 协议文档概览</h2>
            
            <div class="info-box">
                <h4>用户协议 (UserAgreement.vue)</h4>
                <p>包含完整的服务条款，涵盖用户权利义务、服务内容、知识产权等重要条款。</p>
                <ul>
                    <li>服务条款的接受</li>
                    <li>服务内容说明</li>
                    <li>用户注册与账户管理</li>
                    <li>用户行为规范</li>
                    <li>知识产权保护</li>
                    <li>免责声明</li>
                    <li>争议解决机制</li>
                </ul>
            </div>

            <div class="info-box">
                <h4>隐私政策 (PrivacyPolicy.vue)</h4>
                <p>详细说明个人信息收集、使用、存储和保护的政策，符合数据保护法规要求。</p>
                <ul>
                    <li>信息收集类型和目的</li>
                    <li>信息使用和共享规则</li>
                    <li>数据存储和安全措施</li>
                    <li>用户权利和控制</li>
                    <li>未成年人保护</li>
                    <li>Cookie使用说明</li>
                    <li>联系方式和投诉渠道</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🔧 功能特性</h2>
            
            <ul class="feature-list">
                <li>
                    <span class="status-indicator"></span>
                    <strong>模态弹窗设计</strong> - 优雅的弹窗界面，支持点击遮罩关闭
                </li>
                <li>
                    <span class="status-indicator"></span>
                    <strong>响应式布局</strong> - 完美适配桌面、平板、移动设备
                </li>
                <li>
                    <span class="status-indicator"></span>
                    <strong>暗黑模式支持</strong> - 使用CSS变量，完美支持主题切换
                </li>
                <li>
                    <span class="status-indicator"></span>
                    <strong>滚动优化</strong> - 自定义滚动条样式，长内容友好
                </li>
                <li>
                    <span class="status-indicator"></span>
                    <strong>无障碍支持</strong> - 语义化HTML，键盘导航友好
                </li>
                <li>
                    <span class="status-indicator"></span>
                    <strong>交互反馈</strong> - 同意/关闭按钮，支持事件回调
                </li>
            </ul>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🚀 测试功能</h2>
            
            <div class="demo-buttons">
                <a href="http://localhost:3001/register" class="demo-button">
                    前往注册页面测试
                </a>
                <button class="demo-button secondary" onclick="showInfo()">
                    查看技术实现
                </button>
                <button class="demo-button secondary" onclick="showUsage()">
                    使用说明
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">💡 使用方法</h2>
            
            <div class="info-box">
                <h4>1. 在注册页面中集成</h4>
                <p>协议组件已经集成到注册页面中，用户点击协议链接时会弹出相应的协议内容。</p>
            </div>

            <div class="info-box">
                <h4>2. 组件使用方式</h4>
                <p>可以在任何Vue组件中导入和使用：</p>
                <pre style="background: var(--background); padding: 12px; border-radius: 4px; margin: 8px 0; overflow-x: auto;">
import UserAgreement from '@/components/UserAgreement.vue'
import PrivacyPolicy from '@/components/PrivacyPolicy.vue'

// 在模板中使用
&lt;UserAgreement v-if="showAgreement" @close="..." @agree="..." /&gt;
&lt;PrivacyPolicy v-if="showPrivacy" @close="..." @agree="..." /&gt;
                </pre>
            </div>

            <div class="info-box">
                <h4>3. 事件处理</h4>
                <p>组件支持两个事件：</p>
                <ul>
                    <li><strong>@close</strong> - 用户关闭弹窗时触发</li>
                    <li><strong>@agree</strong> - 用户点击"我已阅读并同意"时触发</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            body.setAttribute('data-theme', newTheme);
            
            const toggleButton = document.querySelector('.theme-toggle');
            toggleButton.textContent = newTheme === 'dark' ? '切换亮色模式' : '切换暗黑模式';
        }

        function showInfo() {
            alert('技术实现：\n\n1. Vue 3 Composition API\n2. TypeScript 支持\n3. SCSS 样式\n4. CSS 变量主题系统\n5. 响应式设计\n6. 无障碍优化');
        }

        function showUsage() {
            alert('使用说明：\n\n1. 在注册页面点击协议链接\n2. 弹窗会显示完整的协议内容\n3. 用户可以滚动查看所有内容\n4. 点击"我已阅读并同意"确认\n5. 点击"关闭"或遮罩取消');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户协议和隐私政策演示页面已加载');
        });
    </script>
</body>
</html>
