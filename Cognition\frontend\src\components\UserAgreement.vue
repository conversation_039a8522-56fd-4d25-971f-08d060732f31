<template>
  <div class="agreement-modal" @click.self="$emit('close')">
    <div class="agreement-content">
      <div class="agreement-header">
        <h2 class="agreement-title">用户协议</h2>
        <button @click="$emit('close')" class="close-button" aria-label="关闭">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="agreement-body">
        <div class="agreement-section">
          <h3>1. 服务条款的接受</h3>
          <p>欢迎使用认知测试系统（以下简称"本系统"）。在使用本系统提供的服务之前，请您仔细阅读并充分理解本用户协议（以下简称"本协议"）的各项条款。您的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。</p>
        </div>

        <div class="agreement-section">
          <h3>2. 服务内容</h3>
          <p>本系统为用户提供认知能力测试服务，包括但不限于：</p>
          <ul>
            <li>心理健康量表测评</li>
            <li>认知任务分析</li>
            <li>生理信号采集</li>
            <li>抑郁情绪识别</li>
            <li>精神障碍风险筛查</li>
            <li>个性化测试报告生成</li>
          </ul>
        </div>

        <div class="agreement-section">
          <h3>3. 用户注册与账户</h3>
          <p>3.1 用户需要提供真实、准确、完整的个人信息进行注册。</p>
          <p>3.2 用户有义务及时更新注册信息，确保信息的真实性和有效性。</p>
          <p>3.3 用户应当妥善保管账户信息，不得将账户借给他人使用。</p>
          <p>3.4 如发现账户被盗用或存在安全漏洞，应立即通知我们。</p>
        </div>

        <div class="agreement-section">
          <h3>4. 用户行为规范</h3>
          <p>用户在使用本系统时，应当遵守以下规范：</p>
          <ul>
            <li>遵守国家法律法规和社会公德</li>
            <li>不得利用本系统从事违法违规活动</li>
            <li>不得干扰或破坏系统的正常运行</li>
            <li>不得恶意攻击系统或盗取他人信息</li>
            <li>诚实完成测试，不得故意提供虚假信息</li>
          </ul>
        </div>

        <div class="agreement-section">
          <h3>5. 知识产权</h3>
          <p>5.1 本系统的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护。</p>
          <p>5.2 未经授权，用户不得复制、传播、展示、镜像、上传、下载本系统的任何内容。</p>
          <p>5.3 用户在使用过程中产生的测试数据，我们享有分析和研究的权利（在匿名化处理后）。</p>
        </div>

        <div class="agreement-section">
          <h3>6. 免责声明</h3>
          <p>6.1 本系统提供的测试结果仅供参考，不能替代专业医疗诊断。</p>
          <p>6.2 用户应当理性对待测试结果，如有疑虑应咨询专业医疗机构。</p>
          <p>6.3 我们不对因用户使用本系统而产生的任何直接或间接损失承担责任。</p>
          <p>6.4 因不可抗力因素导致的服务中断或数据丢失，我们不承担责任。</p>
        </div>

        <div class="agreement-section">
          <h3>7. 服务变更与终止</h3>
          <p>7.1 我们有权根据业务需要修改或终止服务内容。</p>
          <p>7.2 如需终止服务，我们将提前30天通知用户。</p>
          <p>7.3 用户违反本协议的，我们有权暂停或终止其账户。</p>
        </div>

        <div class="agreement-section">
          <h3>8. 协议修改</h3>
          <p>我们有权根据法律法规变化或业务需要修改本协议。修改后的协议将在系统内公布，用户继续使用服务即视为接受修改后的协议。</p>
        </div>

        <div class="agreement-section">
          <h3>9. 争议解决</h3>
          <p>因本协议产生的争议，双方应友好协商解决。协商不成的，提交有管辖权的人民法院解决。</p>
        </div>

        <div class="agreement-section">
          <h3>10. 其他条款</h3>
          <p>10.1 本协议的解释权归我们所有。</p>
          <p>10.2 如本协议的任何条款被认定为无效，其余条款仍然有效。</p>
          <p>10.3 本协议自用户接受之日起生效。</p>
        </div>


      </div>
      

    </div>
  </div>
</template>

<script setup lang="ts">
defineEmits<{
  close: []
  agree: []
}>()
</script>

<style lang="scss" scoped>
.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.agreement-content {
  background: var(--card);
  border-radius: 16px;
  box-shadow: var(--shadow-xl);
  max-width: 800px;
  max-height: 90vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;
  border-bottom: 1px solid var(--border);
  margin-bottom: 24px;
}

.agreement-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--foreground);
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;

  &:hover {
    background: var(--muted);
    color: var(--foreground);
  }
}

.agreement-body {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--muted);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 3px;
  }
}

.agreement-section {
  margin-bottom: 24px;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 12px;
  }
  
  p {
    color: var(--muted-foreground);
    line-height: 1.6;
    margin-bottom: 8px;
  }
  
  ul {
    color: var(--muted-foreground);
    line-height: 1.6;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
    }
  }
}

.agreement-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border);
  text-align: center;
  
  p {
    color: var(--muted-foreground);
    font-size: 14px;
    margin-bottom: 8px;
  }
}

.agreement-actions {
  display: flex;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--border);
  justify-content: flex-end;
}

.btn-secondary {
  background: var(--muted);
  color: var(--muted-foreground);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--accent);
    color: var(--foreground);
  }
}

.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: oklch(from var(--primary) calc(l - 0.05) c h);
  }
}

@media (max-width: 768px) {
  .agreement-modal {
    padding: 10px;
  }
  
  .agreement-content {
    max-height: 95vh;
  }
  
  .agreement-header,
  .agreement-body,
  .agreement-actions {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .agreement-title {
    font-size: 20px;
  }
  
  .agreement-actions {
    flex-direction: column;
    
    .btn-secondary,
    .btn-primary {
      width: 100%;
    }
  }
}
</style>
