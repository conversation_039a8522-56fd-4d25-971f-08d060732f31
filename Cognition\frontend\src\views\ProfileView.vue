<template>
  <div class="profile-container">
    <!-- 顶部导航 -->
    <header class="profile-header">
      <button @click="goBack" class="back-button" aria-label="返回">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
        </svg>
      </button>
      <h1 class="page-title">个人信息</h1>
    </header>

    <main class="profile-content">
      <!-- 头像区域 -->
      <section class="avatar-section">
        <div class="avatar-container">
          <div class="avatar-large">
            <img
              :src="getAvatarPath(user?.gender)"
              :alt="`${user?.name}的头像`"
              class="avatar-img"
            />
          </div>
          <button v-if="isEditing" class="change-avatar-btn" aria-label="更换头像">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
            </svg>
          </button>
        </div>
        <div class="user-basic-info">
          <h2 class="user-name">{{ user?.name }}</h2>
          <p class="patient-number">患者编号：{{ user?.patientNumber }}</p>
          <div class="time-info">
            <span class="join-date">注册时间：{{ formatDate(user?.createdAt) }}</span>
            <span class="last-login">最后登录：{{ formatDate(user?.lastLoginAt) }}</span>
          </div>
        </div>
      </section>

      <!-- 个人信息表单 -->
      <section class="info-form-section">
        <form @submit.prevent="handleSave" class="profile-form">
          <!-- 用户信息 -->
          <div class="form-section">
            <h3 class="section-title">用户信息</h3>

            <!-- 第一行：姓名、联系电话、性别 -->
            <div class="form-row-three">
              <div class="form-group">
                <label for="name" class="label">姓名</label>
                <input
                  id="name"
                  v-model="editForm.name"
                  type="text"
                  class="input"
                  :readonly="!isEditing"
                  :class="{ readonly: !isEditing }"
                  required
                  maxlength="20"
                />
              </div>

              <div class="form-group">
                <label for="contactPhone" class="label">联系电话</label>
                <input
                  id="contactPhone"
                  v-model="editForm.contactPhone"
                  type="tel"
                  class="input"
                  :readonly="!isEditing"
                  :class="{ readonly: !isEditing }"
                  pattern="[0-9]{11}"
                />
              </div>

              <div class="form-group">
                <label for="gender" class="label">性别</label>
                <select
                  id="gender"
                  v-model="editForm.gender"
                  class="input"
                  :disabled="!isEditing"
                  :class="{ readonly: !isEditing }"
                >
                  <option value="male">男</option>
                  <option value="female">女</option>
                </select>
              </div>
            </div>

            <!-- 第二行：学历、测试地点、惯用手 -->
            <div class="form-row-three">
              <div class="form-group">
                <label for="education" class="label">学历</label>
                <select
                  id="education"
                  v-model="editForm.education"
                  class="input"
                  :disabled="!isEditing"
                  :class="{ readonly: !isEditing }"
                >
                  <option v-for="edu in educationOptions" :key="edu.value" :value="edu.value">
                    {{ edu.label }}
                  </option>
                </select>
              </div>

              <div class="form-group">
                <label for="idCard" class="label">身份证号</label>
                <input
                  id="idCard"
                  v-model="editForm.idCard"
                  type="text"
                  class="input"
                  :readonly="!isEditing"
                  :class="{ readonly: !isEditing }"
                  pattern="[0-9X]{18}"
                  maxlength="18"
                  :placeholder="isEditing ? '请输入18位身份证号' : ''"
                />
              </div>

              <div class="form-group">
                <label for="dominantHand" class="label">惯用手</label>
                <select
                  id="dominantHand"
                  v-model="editForm.dominantHand"
                  class="input"
                  :disabled="!isEditing"
                  :class="{ readonly: !isEditing }"
                >
                  <option value="right">右手</option>
                  <option value="left">左手</option>
                </select>
              </div>
            </div>

            <!-- 第三行：是否色盲 -->
            <div class="form-row-three">
              <div class="form-group">
                <fieldset class="radio-group">
                  <legend class="label">是否色盲</legend>
                  <div class="radio-options">
                    <label class="radio-label">
                      <input
                        type="radio"
                        v-model="editForm.isColorBlind"
                        :value="false"
                        :disabled="!isEditing"
                        name="colorBlind"
                        class="radio"
                      />
                      <span class="radio-text">否</span>
                    </label>
                    <label class="radio-label">
                      <input
                        type="radio"
                        v-model="editForm.isColorBlind"
                        :value="true"
                        :disabled="!isEditing"
                        name="colorBlind"
                        class="radio"
                      />
                      <span class="radio-text">是</span>
                    </label>
                  </div>
                </fieldset>
              </div>
              <!-- 空白占位 -->
              <div class="form-group"></div>
              <div class="form-group"></div>
            </div>

          </div>

          <!-- 保存按钮 -->
          <div v-if="isEditing" class="form-actions">
            <button
              type="button"
              @click="cancelEdit"
              class="btn btn-outline"
              :disabled="isSaving"
            >
              取消
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isSaving || !hasChanges"
              :aria-busy="isSaving"
            >
              <span v-if="isSaving" class="loading-spinner" aria-hidden="true"></span>
              {{ isSaving ? '保存中...' : '保存修改' }}
            </button>
          </div>
        </form>
      </section>
    </main>

    <!-- 成功提示 -->
    <div
      v-if="showSuccessMessage"
      role="alert"
      class="success-toast"
      aria-live="polite"
    >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
        <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
      </svg>
      个人信息已更新
    </div>

    <!-- 错误提示 -->
    <div
      v-if="errorMessage"
      role="alert"
      class="error-message"
      aria-live="polite"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { educationOptions } from '@/utils/mockData'
import type { User } from '@/types/user'
import dayjs from 'dayjs'

const authStore = useAuthStore()
const router = useRouter()

// 返回函数
const goBack = () => {
  // 尝试返回上一页，如果没有历史记录则跳转到首页
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/home')
  }
}

// 响应式数据
const isEditing = ref(false)
const isSaving = ref(false)
const showSuccessMessage = ref(false)
const errorMessage = ref('')

const editForm = ref<Partial<User>>({})
const originalForm = ref<Partial<User>>({})

// 计算属性
const user = computed(() => authStore.user)

const hasChanges = computed(() => {
  if (!user.value) return false
  
  return Object.keys(editForm.value).some(key => {
    const k = key as keyof User
    return editForm.value[k] !== originalForm.value[k]
  })
})

// 方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY年MM月DD日 HH:mm')
}

const getAvatarPath = (gender?: string) => {
  return gender === 'female' ? '/images/Female.png' : '/images/Male.png'
}

const initEditForm = () => {
  if (user.value) {
    editForm.value = {
      name: user.value.name,
      gender: user.value.gender,
      education: user.value.education,
      contactPhone: user.value.contactPhone,
      idCard: user.value.idCard,
      dominantHand: user.value.dominantHand,
      isColorBlind: user.value.isColorBlind
    }
    originalForm.value = { ...editForm.value }
  }
}



const cancelEdit = () => {
  isEditing.value = false
  editForm.value = { ...originalForm.value }
  errorMessage.value = ''
}

const handleSave = async () => {
  if (!hasChanges.value) {
    isEditing.value = false
    return
  }

  isSaving.value = true
  errorMessage.value = ''

  try {
    const result = await authStore.updateProfile(editForm.value)
    
    if (result.success) {
      isEditing.value = false
      originalForm.value = { ...editForm.value }
      
      // 显示成功提示
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 3000)
    } else {
      errorMessage.value = result.message
    }
  } catch (error) {
    errorMessage.value = '保存失败，请稍后重试'
    console.error('Save profile error:', error)
  } finally {
    isSaving.value = false
  }
}

// 监听用户数据变化
watch(user, (newUser) => {
  if (newUser && !isEditing.value) {
    initEditForm()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  initEditForm()
})
</script>

<style lang="scss" scoped>
// 全局样式覆盖 - 最高优先级
:deep(.input) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(input.input) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(select.input) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(textarea.input) {
  background: transparent !important;
  background-color: transparent !important;
}
.profile-container {
  min-height: 100vh;
  background: var(--background);
}

.profile-header {
  background: var(--card);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 24px;
  position: relative;
  min-height: 60px;
}

.back-button {
  position: absolute;
  left: 24px;
  background: var(--card);
  border: 1px solid var(--border);
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: var(--foreground);
    background: var(--muted);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.page-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
}

.profile-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: $spacing-xl $spacing-lg;

  // 平板横屏模式下直接覆盖宽度
  @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    max-width: 85% !important;
    width: 85% !important;
  }
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: $spacing-xl;
  margin-bottom: $spacing-2xl;
  padding: $spacing-2xl;
  background: var(--card);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);

  @media (max-width: $mobile) {
    flex-direction: column;
    text-align: center;
    gap: $spacing-lg;
  }
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

  .avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.change-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, oklch(0.65 0.15 220), oklch(0.7 0.12 240));
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1) translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}

.user-name {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: $spacing-sm;
}

.patient-number {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  margin-bottom: $spacing-xs;
}

.time-info {
  display: flex;
  gap: $spacing-lg;
  flex-wrap: wrap;
}

.join-date, .last-login {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  margin: 0;
}

.info-form-section {
  background: var(--card);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
  padding: $spacing-2xl;
}

.profile-form {
  .form-section {
    margin-bottom: $spacing-2xl;

    &:last-of-type {
      margin-bottom: $spacing-lg;
    }
  }

  // 确保所有表单输入元素都没有背景色 - 超高优先级
  .form-section input.input,
  .form-section select.input,
  .form-section textarea.input,
  .form-group input.input,
  .form-group select.input,
  .form-group textarea.input,
  input.input,
  select.input,
  textarea.input,
  input,
  input[type="text"],
  input[type="tel"],
  input[type="email"],
  input[type="number"],
  input[type="password"],
  select,
  textarea {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;

    &:focus {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }

    &:hover {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }

    &:active {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }

    &:visited {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }
  }

  // 统一所有表单输入元素的高度和样式
  .form-section input,
  .form-section select,
  .form-group input,
  .form-group select,
  input,
  select {
    min-height: 48px;
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: 8px;
    font-size: $font-size-base;
    color: var(--foreground);
    transition: all 0.3s ease;
    line-height: 1.5;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 2px oklch(from var(--primary) l c h / 0.1);
    }

    &::placeholder {
      color: var(--muted-foreground);
    }

    &.readonly {
      cursor: not-allowed;
      opacity: 0.7;
    }
  }

  // 特别处理select元素的下拉箭头
  select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
  }
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: oklch(0.3 0.02 240);
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-sm;
  border-bottom: 2px solid oklch(0.85 0.02 220);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, oklch(0.65 0.15 220), oklch(0.7 0.12 240));
    border-radius: 1px;
  }
}

.form-group {
  margin-bottom: $spacing-lg;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;

  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

.form-row-three {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: $spacing-md;
  margin-bottom: $spacing-md;

  @media (max-width: $tablet) {
    grid-template-columns: 1fr 1fr;

    .form-group:nth-child(3) {
      grid-column: 1 / -1;
    }
  }

  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

// 强制所有输入框透明背景 - 最高优先级
.profile-container .profile-form input.input,
.profile-container .profile-form select.input,
.profile-container .profile-form textarea.input,
.profile-container input.input,
.profile-container select.input,
.profile-container textarea.input,
.profile-container input,
.profile-container select,
.profile-container textarea,
.profile-container .input {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;

  &:not(.readonly) {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
  }

  &.readonly {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    color: var(--color-muted-foreground);
    cursor: not-allowed;
    border: 1px solid oklch(0.88 0.005 240);
  }
}

// 专门针对下拉控件 - 更高优先级
.profile-container .profile-form select,
.profile-container .profile-form select.input,
.profile-container select,
.profile-container select.input {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;

  option {
    background: white;
    color: #333;
  }
}

.checkbox-group,
.radio-group {
  border: none;
  margin: 0;
  padding: 0;
}

.radio-options {
  display: flex;
  gap: $spacing-lg;
  margin-top: $spacing-xs;
}

.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: $font-size-sm;

  .checkbox,
  .radio {
    margin-right: $spacing-sm;
    width: 16px;
    height: 16px;

    &:disabled {
      cursor: not-allowed;
    }
  }
}

.info-display {
  background: linear-gradient(135deg,
    oklch(0.96 0.005 220) 0%,
    oklch(0.94 0.01 240) 100%);
  border-radius: 12px;
  padding: $spacing-lg;
  border: 1px solid oklch(0.92 0.005 240);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

.info-value {
  font-weight: 500;
  color: var(--color-foreground);
}

.form-actions {
  display: flex;
  gap: $spacing-md;
  justify-content: flex-end;
  padding-top: $spacing-lg;
  border-top: 1px solid var(--color-border);
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: $spacing-sm;
}

.success-toast {
  position: fixed;
  top: $spacing-xl;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, oklch(0.65 0.15 140), oklch(0.7 0.12 160));
  color: white;
  padding: $spacing-md $spacing-lg;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

.error-message {
  position: fixed;
  bottom: $spacing-xl;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-destructive);
  color: var(--color-destructive-foreground);
  padding: $spacing-md $spacing-lg;
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-width: 90vw;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

// 平板横屏优化 - 最大化卡片宽度，一屏完全展示
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .profile-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .profile-header {
    flex-shrink: 0;
    padding: $spacing-xs $spacing-md;
    min-height: 48px;
  }

  .profile-container .profile-content {
    flex: 1 !important;
    max-width: 85% !important;
    width: 85% !important;
    padding: $spacing-xs $spacing-sm !important;
    height: calc(100vh - 48px) !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .info-form-section {
    width: 100%;
    max-width: none;
  }

  .avatar-section {
    flex-shrink: 0;
    padding: $spacing-xs $spacing-sm;
    margin-bottom: $spacing-xs;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: $spacing-sm;
    min-height: 60px;
  }

  .avatar-large {
    width: 48px;
    height: 48px;
  }

  .user-basic-info {
    text-align: left;

    .user-name {
      font-size: $font-size-sm;
      margin-bottom: 1px;
      font-weight: 600;
      line-height: 1.2;
    }

    .patient-number {
      font-size: $font-size-xs;
      margin-bottom: 1px;
      line-height: 1.1;
    }

    .time-info {
      display: flex;
      gap: $spacing-sm;
      flex-wrap: wrap;
    }

    .join-date, .last-login {
      font-size: $font-size-xs;
      margin: 0;
      line-height: 1.1;
    }
  }

  .profile-form {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: $spacing-md;
    align-content: start;
    min-height: 0;
    overflow: hidden;
  }

  .form-section {
    padding: $spacing-xs $spacing-sm;
    margin-bottom: 0;
    height: fit-content;

    &:last-child {
      grid-column: 1 / -1;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: $spacing-md;
      padding: $spacing-xs 0;
    }
  }

  .section-title {
    font-size: $font-size-sm;
    margin-bottom: $spacing-xs;
    font-weight: 600;
    color: var(--primary);
    border-bottom: 1px solid var(--border);
    padding-bottom: 2px;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-sm;
  }

  .form-row-three {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: $spacing-sm;
    margin-bottom: $spacing-xs;
  }

  .form-group {
    margin-bottom: $spacing-xs;
  }

  .label {
    font-size: $font-size-xs;
    margin-bottom: 2px;
    font-weight: 500;
    line-height: 1.2;
  }

  .input {
    min-height: 36px;
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
    border-radius: 6px;
    line-height: 1.2;
  }

  .form-actions {
    grid-column: 1 / -1;
    justify-content: center;
    gap: $spacing-md;
    margin-top: $spacing-xs;
    padding: $spacing-xs 0;

    .btn {
      min-width: 80px;
      min-height: 32px;
      padding: $spacing-xs $spacing-md;
      font-size: $font-size-xs;
    }
  }

  .info-display {
    display: grid;
    grid-template-columns: 1fr;
    gap: $spacing-xs;
  }

  .info-item {
    padding: $spacing-xs;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 28px;

    .info-label {
      font-size: $font-size-xs;
      margin-bottom: 0;
      font-weight: 500;
    }

    .info-value {
      font-size: $font-size-xs;
      text-align: right;
    }
  }

  // 确保所有内容紧凑显示
  * {
    line-height: 1.2;
  }

  .checkbox-group {
    margin-bottom: $spacing-xs;
  }

  .checkbox-item {
    padding: 2px 0;
    font-size: $font-size-xs;
    line-height: 1.2;
  }

  // 进一步压缩间距
  .form-group:last-child {
    margin-bottom: 0;
  }

  .section-title:first-child {
    margin-top: 0;
  }
}

// 平板竖屏优化
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .profile-content {
    max-width: 85%;
    padding: $spacing-xl;
  }

  .avatar-section {
    padding: $spacing-xl;
  }

  .form-section {
    padding: $spacing-lg;
  }

  .input {
    min-height: 48px;
    font-size: $font-size-base;
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .profile-content {
    padding: $spacing-lg $spacing-md;
  }

  .form-actions {
    flex-direction: column-reverse;

    .btn {
      width: 100%;
    }
  }
}
</style>