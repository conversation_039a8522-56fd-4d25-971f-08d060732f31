<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <header class="top-nav">
      <div class="nav-content">
        <div class="user-info">
          <div class="avatar">
            <img
              :src="getAvatarPath(user?.gender)"
              :alt="`${user?.name}的头像`"
              class="avatar-img"
            />
          </div>
          <div class="user-details">
            <h2 class="user-name">{{ user?.name || '用户' }}</h2>
            <p class="user-id">{{ user?.patientNumber }}</p>
          </div>
        </div>
        
        <div class="menu-wrapper">
          <button
            @click="showUserMenu = !showUserMenu"
            class="menu-button"
            :aria-expanded="showUserMenu"
            aria-label="用户菜单"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </button>

          <!-- 用户菜单下拉 -->
          <div v-if="showUserMenu" class="user-menu" @click="showUserMenu = false">
            <router-link to="/profile" class="menu-item">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
              </svg>
              个人信息
            </router-link>
            <router-link to="/test-history" class="menu-item">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
              测试记录
            </router-link>
            <button @click="handleLogout" class="menu-item logout-item">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"/>
              </svg>
              退出登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="welcome-section">
        <div class="greeting-header">
          <div class="greeting-text">
            <h1 class="greeting-title">
              {{ getGreeting() }}，{{ user?.name }}！
            </h1>
            <p class="greeting-subtitle">
              欢迎使用认知测试系统，开始您的认知能力评估之旅
            </p>
          </div>
          
          <!-- 快速统计 -->
          <div class="quick-stats">
            <div class="stat-item">
              <div class="stat-value">{{ completedTests }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-value">{{ mainTestsCompleted ? availableTasks.length : getMainTestTasks().length }}</div>
              <div class="stat-label">{{ mainTestsCompleted ? '可重测' : '主要测试' }}</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-value">{{ getProgressPercentage() }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 测试进度区域 
      <section v-if="!mainTestsCompleted" class="test-progress-section">
        <div class="progress-header">
          <h2 class="section-title">🏆 认知能力综合测试</h2>
          <div class="progress-info">
            <span class="progress-text">完成进度：{{ suiteProgress }}%</span>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: suiteProgress + '%' }"></div>
            </div>
          </div>
        </div>
     
        
        <div class="progress-description">
          <p>请按顺序完成以下所有测试项目，以获得完整的认知能力评估报告。</p>
          <p class="highlight-text">✨ 完成所有测试后，您可以选择单项重测以提升成绩。</p>
        </div>
      </section> -->

      <!-- 可用测试任务 -->
      <section class="test-tasks">
        <div class="section-header">
          <h2 class="section-title">
            {{ mainTestsCompleted ? '🔄 单项重测' : '📝 测试任务' }}
          </h2>
          <div class="task-count">共 {{ availableTasks.length }} 个测试</div>
        </div>
        
        <div v-if="mainTestsCompleted" class="retest-notice">
          <div class="notice-content">
            <div class="notice-icon">🎉</div>
            <div class="notice-text">
              <h3>恭喜！您已完成所有主要测试</h3>
              <p>现在您可以选择任意测试项目进行重测，以提升您的成绩。</p>
            </div>
          </div>
        </div>
        
        <!-- 测试套件 -->
        <div class="test-suites-accordion">
          <div 
            v-for="suite in testSuites" 
            :key="suite.id" 
            class="suite-accordion-item"
          >
            <!-- 一级套件信息 -->
            <div class="suite-header" @click="toggleSuite(suite.id)">
              <div class="suite-main-info">
                <div class="suite-title-section">
                  <h3 class="suite-name">{{ suite.name }}</h3>
                  <div class="suite-meta">
                    <span class="suite-number">编号：{{ suite.suiteNumber }}</span>
                    <span class="suite-date">创建日期：{{ formatDateSimple(suite.createdDate) }}</span>
                  </div>
                </div>
                
                <div class="suite-actions">
                  <button
                    @click.stop="startMainTestSuite"
                    class="suite-start-btn"
                    :disabled="suite.status === 'completed'"
                  >
                    {{ getSuiteButtonText(suite) }}
                  </button>



                  <button class="suite-expand-btn" :class="{ 'expanded': expandedSuite === suite.id }">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                    </svg>
                  </button>
                </div>
              </div>
              
              <!-- 进度条 -->
              <div class="suite-progress">
                <div class="suite-progress-bar">
                  <div class="suite-progress-fill" :style="{ width: suiteProgress + '%' }"></div>
                </div>
                <span class="suite-progress-text">{{ suiteProgress }}% 已完成</span>
              </div>
            </div>
            
            <!-- 展开的任务列表 -->
            <div v-if="expandedSuite === suite.id" class="suite-content">
              <div class="tasks-list">
                <div 
                  v-for="task in getTasksBySuite(suite.id)" 
                  :key="task.id" 
                  class="task-accordion-item"
                >
                  <!-- 任务头部 -->
                  <div class="task-header" @click="toggleTask(task.id)">
                    <div class="task-basic-info">
                      <div class="task-icon">{{ task.icon }}</div>
                      <div class="task-main">
                        <h4 class="task-name">{{ task.name }}</h4>
                        <div class="task-brief">
                          <span class="task-type">{{ task.category }}</span>
                          <span class="task-duration">约{{ task.estimatedDuration }}分钟</span>
                          <span :class="['task-difficulty-badge', task.difficulty]">
                            {{ getDifficultyText(task.difficulty) }}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="task-status-section">
                      <div class="task-completion-info">
                        <div class="task-completion-status">
                          {{ getTaskCompletionStatus(task.id) }}
                        </div>
                        <div class="task-completion-rate">
                          {{ getTaskCompletionRate(task.id) }}%
                        </div>
                      </div>

                      <button class="task-expand-btn" :class="{ 'expanded': expandedTask === task.id }">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <!-- 展开的任务详情 -->
                  <div v-if="expandedTask === task.id" class="task-details">
                    <div class="task-description-section">
                      <h5>测试单位</h5>
                      <p class="task-full-description">{{ task.fullDescription || task.description }}</p>
                    </div>
                    
                    <div v-if="task.instructions" class="task-instructions">
                      <h5>测试指导</h5>
                      <ul class="instructions-list">
                        <li v-for="instruction in task.instructions" :key="instruction">
                          {{ instruction }}
                        </li>
                      </ul>
                    </div>
                    
                    <div class="task-detail-actions">
                      <button
                        @click="startTest(task)"
                        class="task-start-btn"
                        :disabled="!task.isAvailable"
                      >
                        {{ getTaskButtonText(task) }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 测试记录列表 -->
      <section class="test-records">
        <div class="section-header">
          <h2 class="section-title">历史测试记录</h2>
          <router-link to="/test-history" class="view-all-link">
            查看全部记录
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </router-link>
        </div>
        
        <!-- 手风琴式测试记录 -->
        <div v-if="Object.keys(groupedTestRecords).length > 0" class="records-accordion">
          <div 
            v-for="(records, date) in groupedTestRecords" 
            :key="date" 
            class="record-date-group"
          >
            <!-- 日期头部 -->
            <div class="date-header" @click="toggleRecordDate(date)">
              <div class="date-info">
                <h3 class="date-title">{{ formatDateChinese(date) }}</h3>
                <span class="date-count">{{ records.length }} 次测试</span>
              </div>
              
              <div class="date-stats">
                <div class="completion-rate">
                  <span class="rate-text">完成率</span>
                  <span class="rate-value">{{ getDateCompletionRate(records) }}%</span>
                </div>

                <!-- 任务测试报告按钮 - 只在完成率100%时显示 -->
                <button
                  v-if="getDateCompletionRate(records) === 100"
                  @click.stop="viewTaskReport(date, records)"
                  class="task-report-btn"
                >
                  📊 查看测试报告
                </button>

                <button class="date-expand-btn" :class="{ 'expanded': expandedRecordDate === date }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- 展开的记录列表 -->
            <div v-if="expandedRecordDate === date" class="date-records">
              <div 
                v-for="record in records" 
                :key="record.id" 
                class="record-accordion-item"
              >
                <!-- 记录头部 -->
                <div class="record-header" @click="toggleRecord(record.id)">
                  <div class="record-basic-info">
                    <div class="record-icon">
                      <span>{{ getTestIcon(record.testType) }}</span>
                    </div>
                    
                    <div class="record-main-info">
                      <h4 class="record-name">{{ record.testName }}</h4>
                      <div class="record-meta">
                        <span class="record-time">{{ formatTime(record.startTime) }}</span>
                        <span v-if="record.duration" class="record-duration">
                          用时 {{ Math.round(record.duration / 60) }} 分钟
                        </span>
                        <span v-if="record.score" class="record-score-preview">
                          {{ record.score }} 分
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="record-status-section">
                    <span :class="['record-status-badge', `status-${record.status}`]">
                      {{ getStatusText(record.status) }}
                    </span>
                    
                    <button class="record-expand-btn" :class="{ 'expanded': expandedRecord === record.id }">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                      </svg>
                    </button>
                  </div>
                </div>
                
                <!-- 展开的记录详情 -->
                <div v-if="expandedRecord === record.id" class="record-details">
                  <div v-if="record.status === 'completed'" class="record-summary">
                    <div class="summary-item">
                      <span class="summary-label">测试日期</span>
                      <span class="summary-value">{{ formatDateTime(record.startTime) }}</span>
                    </div>
                    <div v-if="record.endTime" class="summary-item">
                      <span class="summary-label">结束日期</span>
                      <span class="summary-value">{{ formatDateTime(record.endTime) }}</span>
                    </div>
                    <div v-if="record.score" class="summary-item">
                      <span class="summary-label">测试得分</span>
                      <span class="summary-value score-highlight">{{ record.score }} 分</span>
                    </div>
                    <div v-if="record.results?.overallPercentile" class="summary-item">
                      <span class="summary-label">百分位数</span>
                      <span class="summary-value">{{ record.results.overallPercentile }}%</span>
                    </div>
                  </div>
                  
                  <div class="record-actions">
                    <button
                      v-if="record.status === 'completed'"
                      @click="viewTestResult(record)"
                      class="view-result-btn"
                    >
                      📈 查看测试结果
                    </button>

                    <!-- 历史记录中不显示重新测试按钮，只能查看结果 -->
                    
                    <button 
                      v-if="record.status === 'inProgress'"
                      @click="continueTest(record)"
                      class="continue-btn"
                    >
                      ▶️ 继续测试
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-records">
          <div class="empty-icon">📋</div>
          <p class="empty-text">暂无测试记录</p>
          <p class="empty-hint">完成测试后，记录将显示在这里</p>
          <button @click="mainTestsCompleted ? startFirstTest : startMainTestSuite" class="start-first-test-btn">
            {{ mainTestsCompleted ? '开始重测' : '开始主要测试' }}
          </button>
        </div>
      </section>
    </main>

    <!-- 测试报告模态框 -->
    <div v-if="showReportModal" class="report-modal-overlay" @click="closeReportModal">
      <div class="report-modal" @click.stop>
        <div class="report-modal-header">
          <h3 class="report-modal-title">
            {{ currentReport?.isTaskReport ? '📊' : '📈' }}
            {{ currentReport?.isTaskReport ? currentReport?.taskName || currentReport?.testName : currentReport?.testName }}
            {{ currentReport?.isTaskReport ? '测试报告' : '测试结果' }}
          </h3>
          <button @click="closeReportModal" class="report-modal-close" aria-label="关闭报告">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div class="report-modal-content">
          <div v-if="currentReport" class="report-details">
            <!-- 核心指标 -->
            <div class="report-metrics">
              <div class="metric-card score-card">
                <div class="metric-icon">🎯</div>
                <div class="metric-info">
                  <div class="metric-label">测试得分</div>
                  <div class="metric-value">{{ currentReport.score }} 分</div>
                </div>
              </div>

              <div class="metric-card percentile-card">
                <div class="metric-icon">📊</div>
                <div class="metric-info">
                  <div class="metric-label">百分位数</div>
                  <div class="metric-value">{{ currentReport.percentile }}%</div>
                </div>
              </div>

              <!-- 任务级别额外信息 -->
              <div v-if="currentReport.isTaskReport && currentReport.totalAttempts" class="metric-card attempts-card">
                <div class="metric-icon">🔄</div>
                <div class="metric-info">
                  <div class="metric-label">测试次数</div>
                  <div class="metric-value">{{ currentReport.totalAttempts }} 次</div>
                </div>
              </div>
            </div>

            <!-- 成绩解释 -->
            <div class="report-section">
              <h4 class="section-title">📋 成绩解释</h4>
              <p class="interpretation-text">{{ currentReport.interpretation }}</p>
            </div>

            <!-- 对比分析 -->
            <div class="report-section">
              <h4 class="section-title">📈 对比分析</h4>
              <div class="comparison-card">
                <div class="comparison-item">
                  <span class="comparison-label">您的表现</span>
                  <span :class="['comparison-status', currentReport.comparisonData.isAboveAverage ? 'above-average' : 'below-average']">
                    {{ currentReport.comparisonData.isAboveAverage ? '高于' : '低于' }}平均水平
                    {{ Math.abs(currentReport.comparisonData.percentageDiff) }}%
                  </span>
                </div>
                <div class="comparison-item">
                  <span class="comparison-label">同龄人平均分</span>
                  <span class="comparison-value">{{ currentReport.comparisonData.averageScore }} 分</span>
                </div>
              </div>
            </div>

            <!-- 建议 -->
            <div v-if="currentReport.recommendations && currentReport.recommendations.length > 0" class="report-section">
              <h4 class="section-title">💡 改进建议</h4>
              <ul class="recommendations-list">
                <li v-for="(rec, index) in currentReport.recommendations" :key="index" class="recommendation-item">
                  {{ rec }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="report-modal-footer">
          <button @click="closeReportModal" class="modal-btn modal-btn-primary">
            知道了
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { showToast } from 'vant'
import { 
  getUserTestRecords, 
  getMainTestTasks, 
  getRetestAvailableTasks,
  calculateSuiteProgress,
  canRetestIndividualTasks,
  testSuites,
  availableTestTasks,
  getGroupedTestRecords,
  generateTestReport,
  type TestTask 
} from '@/utils/mockData'
import type { TestRecord } from '@/types/user'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const showUserMenu = ref(false)
const recentTests = ref<TestRecord[]>([])
const groupedTestRecords = ref<Record<string, TestRecord[]>>({})
const availableTasks = ref<TestTask[]>([])
const mainTestsCompleted = ref(false)
const suiteProgress = ref(0)
const expandedSuite = ref<string | null>(null) // 当前展开的套件ID
const expandedTask = ref<string | null>(null) // 当前展开的任务ID
const expandedRecordDate = ref<string | null>(null) // 展开的记录日期
const expandedRecord = ref<string | null>(null) // 展开的记录ID
const showReportModal = ref(false) // 显示测试报告模态框
const currentReport = ref<any>(null) // 当前查看的报告数据

// 加载测试任务
const loadTestTasks = () => {
  if (!user.value) return
  
  const completedTaskIds = recentTests.value
    .filter(record => record.status === 'completed')
    .map(record => getTaskIdFromType(record.testType))
  
  // 检查主要测试是否完成
  mainTestsCompleted.value = canRetestIndividualTasks(completedTaskIds)
  
  // 计算进度
  suiteProgress.value = calculateSuiteProgress('main_cognitive_suite', completedTaskIds)
  
  // 获取可用测试任务
  if (mainTestsCompleted.value) {
    // 主要测试完成后，可以进行单项重测
    availableTasks.value = getRetestAvailableTasks(completedTaskIds)
  } else {
    // 主要测试未完成，只显示主要测试任务
    availableTasks.value = getMainTestTasks()
  }
}

// 根据测试类型获取任务ID
const getTaskIdFromType = (testType: string): string => {
  const typeToIdMap: Record<string, string> = {
    'PDQ5': 'task_pdq5',
    'Hopkins': 'task_hopkins',
    'NBack': 'task_nback',
    'Stroop': 'task_stroop',
    'TrailMaking': 'task_trail',
    'VerbalFluency': 'task_fluency',
    'CPT': 'task_cpt',
    'DSST': 'task_dsst'
  }
  return typeToIdMap[testType] || ''
}

// 计算属性
const user = computed(() => authStore.user)

const completedTests = computed(() => 
  recentTests.value.filter(test => test.status === 'completed').length
)

const getProgressPercentage = () => {
  return suiteProgress.value
}

// 方法
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const getAvatarPath = (gender?: string) => {
  return gender === 'female' ? '/images/Female.png' : '/images/Male.png'
}

const getDifficultyText = (difficulty: string) => {
  const difficultyMap = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return difficultyMap[difficulty as keyof typeof difficultyMap] || difficulty
}

const startTest = async (task: TestTask) => {
  if (!task.isAvailable) return
  
  // 检查是否为重测
  const isRetesting = mainTestsCompleted.value
  const completedTaskIds = recentTests.value
    .filter(record => record.status === 'completed')
    .map(record => getTaskIdFromType(record.testType))
  const hasCompleted = completedTaskIds.includes(task.id)
  
  let confirmMessage = `即将开始 ${task.name}\n\n`
  confirmMessage += `• 预计耗时：${task.estimatedDuration} 分钟\n`
  confirmMessage += `• 测试类型：${task.category}\n`
  confirmMessage += `• 难度等级：${getDifficultyText(task.difficulty)}\n`
  
  if (isRetesting && hasCompleted) {
    confirmMessage += `\n🔄 这是一次重测，您之前已完成过此测试。\n`
    confirmMessage += `重测可以帮助您提升成绩和熟练度。\n`
  } else if (!isRetesting) {
    confirmMessage += `\n🏆 这是主要测试的一部分，完成后将统计在整体进度中。\n`
  }
  
  confirmMessage += `\n请确保您有充足的时间完成测试，测试过程中请勿中断。\n\n确定开始吗？`
  
  const confirmed = confirm(confirmMessage)
  
  if (confirmed) {
    try {
      // 记录测试开始
      console.log('开始测试:', task.name, task.type, isRetesting ? '(重测)' : '(首次)')
      
      // 根据测试类型跳转到对应页面
      if (task.type === 'PDQ5') {
        await router.push('/tests/pdq5/instructions')
      } else {
        // 其他测试页面开发完成后添加对应路由
        const statusText = isRetesting && hasCompleted ? '重测' : '首次测试'
        alert(`${task.name} (${statusText}) 页面正在开发中...\n\n即将跳转到测试列表页面查看所有可用测试。`)
        await router.push('/tests')
      }
    } catch (error) {
      console.error('启动测试失败:', error)
      alert('启动测试失败，请稍后重试')
    }
  }
}

const getTestIcon = (testType: string) => {
  const iconMap: Record<string, string> = {
    PDQ5: '🧠',
    Hopkins: '📚',
    NBack: '🔢',
    Stroop: '🎨',
    TrailMaking: '🔗',
    VerbalFluency: '💬',
    CPT: '⏱️',
    DSST: '🔢'
  }
  return iconMap[testType] || '📝'
}

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MM月DD日 HH:mm')
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待开始',
    inProgress: '进行中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const handleLogout = async () => {
  if (confirm('确定要退出登录吗？')) {
    try {
      authStore.logout()
      await router.push('/login')
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }
}

// 切换记录日期展开状态
const toggleRecordDate = (date: string) => {
  expandedRecordDate.value = expandedRecordDate.value === date ? null : date
  expandedRecord.value = null // 关闭记录展开
}

// 切换记录展开状态
const toggleRecord = (recordId: string) => {
  expandedRecord.value = expandedRecord.value === recordId ? null : recordId
}

// 获取日期完成率
const getDateCompletionRate = (records: TestRecord[]): number => {
  const completed = records.filter(r => r.status === 'completed').length
  return records.length > 0 ? Math.round((completed / records.length) * 100) : 0
}

// 查看任务测试报告（按日期分组的任务综合报告）
const viewTaskReport = (date: string, records: any[]) => {
  // 获取该日期下所有完成的记录
  const completedRecords = records.filter(record => record.status === 'completed')

  if (completedRecords.length === 0) {
    showToast('暂无测试报告')
    return
  }

  // 计算综合得分（该日期所有测试的平均分）
  const totalScore = completedRecords.reduce((sum, record) => sum + (record.score || 0), 0)
  const averageScore = Math.round(totalScore / completedRecords.length)

  // 生成任务级别的综合报告
  const report = {
    isTaskReport: true,
    taskName: `${formatDateChinese(date)} 测试任务`,
    testName: `${formatDateChinese(date)} 测试任务`,
    testCount: completedRecords.length, // 该日期完成的测试数量
    score: averageScore,
    percentile: Math.floor(Math.random() * 40) + 60, // 模拟百分位数
    analysis: `基于 ${completedRecords.length} 个测试项目的综合分析显示，您在${formatDateChinese(date)}的整体表现${averageScore > 80 ? '优秀' : averageScore > 70 ? '良好' : '中等'}。各项认知能力指标表现均衡。`,
    recommendations: [
      '恭喜完成当日的认知能力评估',
      '建议定期进行复测以跟踪认知能力变化',
      '可以针对薄弱环节进行专项训练',
      '保持健康的生活方式有助于认知能力维护'
    ]
  }

  currentReport.value = report
  showReportModal.value = true
}

// 查看任务套件测试报告（整个任务套件的综合报告）
const viewSuiteReport = (suite: any) => {
  // 获取该任务套件下所有测试项目的完成记录
  const suiteRecords = recentTests.value.filter(record =>
    record.status === 'completed'
  )

  if (suiteRecords.length === 0) {
    showToast('暂无测试记录')
    return
  }

  // 计算综合得分（所有测试项目的平均分）
  const totalScore = suiteRecords.reduce((sum, record) => sum + (record.score || 0), 0)
  const averageScore = Math.round(totalScore / suiteRecords.length)

  // 生成任务套件级别的综合报告
  const report = {
    isTaskReport: true,
    taskName: suite.name,
    testName: suite.name,
    testCount: suiteRecords.length, // 任务套件特有的测试项目完成数量
    score: averageScore,
    percentile: Math.floor(Math.random() * 40) + 60, // 模拟百分位数
    analysis: `基于 ${suiteRecords.length} 个测试项目的综合分析显示，您在${suite.name}中的整体表现${averageScore > 80 ? '优秀' : averageScore > 70 ? '良好' : '中等'}。各项认知能力指标均衡发展。`,
    recommendations: [
      '恭喜完成完整的认知能力评估',
      '建议定期进行复测以跟踪认知能力变化',
      '可以针对薄弱环节进行专项训练',
      '保持健康的生活方式有助于认知能力维护'
    ]
  }

  currentReport.value = report
  showReportModal.value = true
}

// 查看测试结果（单次测试的具体结果）
const viewTestResult = (record: TestRecord) => {
  const report = generateTestReport(record)
  if (!report) {
    showToast('暂无测试结果')
    return
  }

  // 标记为结果查看（非报告）
  ;(report as any).isTaskReport = false

  currentReport.value = report
  showReportModal.value = true
}

// 关闭报告模态框
const closeReportModal = () => {
  showReportModal.value = false
  currentReport.value = null
}

// 从记录重测
const retestFromRecord = (record: TestRecord) => {
  const task = availableTestTasks.find(t => t.type === record.testType)
  if (task) {
    startTest(task)
  }
}

// 继续测试
const continueTest = (record: TestRecord) => {
  const confirmed = confirm(`继续进行 ${record.testName}？\n\n测试将从上次中断的地方继续。`)
  if (confirmed) {
    alert('正在跳转到测试页面...')
    // 这里实际应该跳转到测试页面并恢复状态
  }
}

// 格式化中文日期
const formatDateChinese = (dateString: string): string => {
  return dayjs(dateString).format('YYYY年MM月DD日')
}

// 格式化时间
const formatTime = (dateString: string): string => {
  return dayjs(dateString).format('HH:mm')
}

// 格式化日期时间
const formatDateTime = (dateString: string): string => {
  return dayjs(dateString).format('YYYY年MM月DD日 HH:mm')
}

// 开始首个测试
const startFirstTest = () => {
  const firstAvailableTask = availableTasks.value.find(task => task.isAvailable)
  if (firstAvailableTask) {
    startTest(firstAvailableTask)
  } else {
    alert('暂无可用测试')
  }
}

// 开始主要测试套件
const startMainTestSuite = () => {
  // 直接跳转到认知评估启动页面
  router.push('/cognitive-assessment')
}

// 切换套件展开状态
const toggleSuite = (suiteId: string) => {
  expandedSuite.value = expandedSuite.value === suiteId ? null : suiteId
  expandedTask.value = null // 关闭任务展开
}

// 切换任务展开状态
const toggleTask = (taskId: string) => {
  expandedTask.value = expandedTask.value === taskId ? null : taskId
}

// 根据套件ID获取任务列表
const getTasksBySuite = (suiteId: string) => {
  const suite = testSuites.find(s => s.id === suiteId)
  if (!suite) return []
  
  return availableTestTasks
    .filter(task => suite.tasks.includes(task.id))
    .sort((a, b) => a.order - b.order)
}

// 获取任务完成率
const getTaskCompletionRate = (taskId: string): number => {
  // 对于单个测试任务，检查是否有完成的记录
  const testType = availableTestTasks.find(t => t.id === taskId)?.type
  if (!testType) return 0

  const hasCompleted = recentTests.value.some(record =>
    record.testType === testType && record.status === 'completed'
  )

  return hasCompleted ? 100 : 0
}

// 获取任务完成状态（基于完成率）
const getTaskCompletionStatus = (taskId: string) => {
  const completionRate = getTaskCompletionRate(taskId)

  if (completionRate === 100) return '已完成'

  // 检查是否有进行中的记录
  const testType = availableTestTasks.find(t => t.id === taskId)?.type
  if (testType) {
    const inProgress = recentTests.value.some(record =>
      record.testType === testType && record.status === 'inProgress'
    )
    if (inProgress) return '进行中'
  }

  return '未开始'
}

// 检查任务是否100%完成（用于显示报告按钮）
const isTaskFullyCompleted = (taskId: string): boolean => {
  return getTaskCompletionRate(taskId) === 100
}

// 获取套件按钮文本
const getSuiteButtonText = (suite: any) => {
  if (suite.status === 'completed') return '已完成'
  if (suiteProgress.value === 0) return '立即开始测试'
  return '继续测试'
}

// 获取测试项目按钮文本（正在进行的测试任务）
const getTaskButtonText = (task: any) => {
  const completionRate = getTaskCompletionRate(task.id)
  const status = getTaskCompletionStatus(task.id)

  if (completionRate === 0) return '开始测试'
  if (status === '进行中') return '继续测试'
  if (completionRate === 100) return '继续测试' // 支持多次测试
  return '开始测试'
}

// 查看测试结果（旧版本，待删除）
const viewTestResultOld = (task: TestTask) => {
  const testRecord = recentTests.value.find(record => 
    record.testType === task.type && record.status === 'completed'
  )
  
  if (testRecord) {
    let resultMessage = `${task.name} 测试结果\n\n`
    resultMessage += `开始时间：${formatDate(testRecord.startTime)}\n`
    if (testRecord.endTime) {
      resultMessage += `结束时间：${formatDate(testRecord.endTime)}\n`
    }
    if (testRecord.duration) {
      resultMessage += `用时：${Math.round(testRecord.duration / 60)} 分钟\n`
    }
    if (testRecord.score !== undefined) {
      resultMessage += `得分：${testRecord.score} 分\n`
    }
    resultMessage += `\n点击“重新测试”可以提升成绩。`
    
    alert(resultMessage)
  } else {
    alert('暂无测试结果')
  }
}

// 格式化日期 (重写以避免重复)
const formatDateSimple = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD')
}

const loadRecentTests = () => {
  if (user.value) {
    recentTests.value = getUserTestRecords(user.value.id)
      .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())

    // 加载分组记录
    groupedTestRecords.value = getGroupedTestRecords(user.value.id)

    // 数据加载完成后，立即加载任务状态
    loadTestTasks()
  }
}

// 生命周期
// 平板优化功能
const initTabletOptimization = () => {
  // 检测设备类型
  const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1024
  const isLandscape = window.innerWidth > window.innerHeight
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  if (isTablet && isLandscape && isTouchDevice) {
    console.log('🏠 检测到平板横屏设备，开始优化主页卡片布局...')

    // 动态加载优化CSS
    const cssId = 'home-page-tablet-optimization'
    if (!document.getElementById(cssId)) {
      const css = `
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
          .test-suites-accordion {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)) !important;
            gap: 24px !important;
            margin-bottom: 32px !important;
          }

          .suite-accordion-item {
            width: 100% !important;
            max-width: none !important;
            margin-bottom: 0 !important;
          }

          .suite-header {
            padding: 24px !important;
            min-height: 120px !important;
          }

          .suite-main-info {
            flex-direction: column !important;
            gap: 16px !important;
            align-items: flex-start !important;
          }

          .suite-actions {
            align-self: stretch !important;
            justify-content: space-between !important;
          }

          .suite-start-btn {
            flex: 1 !important;
            max-width: 200px !important;
            min-height: 56px !important;
            font-size: 16px !important;
            padding: 16px 24px !important;
          }

          .records-accordion {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
            gap: 20px !important;
          }

          .record-date-group {
            margin-bottom: 0 !important;
          }

          .tasks-list {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
            gap: 16px !important;
            padding: 24px !important;
          }

          .task-accordion-item {
            margin-bottom: 0 !important;
          }
        }
      `

      const style = document.createElement('style')
      style.id = cssId
      style.textContent = css
      document.head.appendChild(style)

      console.log('✅ 主页平板优化CSS已注入')
    }
  }
}

// 监听窗口大小变化
let resizeTimeout: number | null = null
const handleResize = () => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
  resizeTimeout = setTimeout(() => {
    initTabletOptimization()
  }, 300)
}

onMounted(() => {
  // 加载测试记录（会自动触发任务状态加载）
  loadRecentTests()

  // 初始化平板优化
  initTabletOptimization()

  // 监听窗口变化
  window.addEventListener('resize', handleResize)
  window.addEventListener('orientationchange', handleResize)

  // 点击外部关闭菜单
  document.addEventListener('click', (e) => {
    if (!e.target || !(e.target as Element).closest('.user-menu, .menu-button')) {
      showUserMenu.value = false
    }
  })
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('orientationchange', handleResize)

  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
})
</script>

<style lang="scss" scoped>
.home-container {
  min-height: 100vh;
  background: var(--background);
  width: 100%;
}

.top-nav {
  background: var(--card);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  overflow: visible;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-md $spacing-lg;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  
  .avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--primary);
    color: var(--primary-foreground);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: $font-size-lg;
  }
}

.user-name {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 2px;
}

.user-id {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
}

.menu-wrapper {
  position: relative;
}

.menu-button {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: $spacing-sm;
  border-radius: var(--radius);
  transition: all 0.2s ease;

  &:hover {
    color: var(--foreground);
    background: var(--muted);
  }
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  overflow: hidden;
  z-index: 20;
  margin-top: 4px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-md;
  color: var(--foreground);
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;
  font-size: $font-size-sm;
  
  &:hover {
    background: var(--muted);
  }
  
  &.logout-item {
    color: var(--destructive);
    border-top: 1px solid var(--border);
  }
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-xl $spacing-lg;
  overflow: visible;
  position: relative;
}

.welcome-section {
  margin-bottom: $spacing-xl;
}

.greeting-header {
  text-align: left;
  margin-bottom: $spacing-xl;
  padding: $spacing-lg;
  background: linear-gradient(135deg, 
    oklch(from var(--primary) l c h / 0.05) 0%, 
    oklch(from var(--secondary) l c h / 0.05) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid oklch(from var(--primary) l c h / 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: $spacing-xl;
  
  @media (max-width: $tablet) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-lg;
  }
}

.greeting-text {
  flex: 1;
}

.quick-stats {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  padding: $spacing-md;
  background: rgba(255, 255, 255, 0.7);
  border-radius: var(--radius);
  backdrop-filter: blur(10px);
  
  @media (max-width: $tablet) {
    align-self: stretch;
    justify-content: space-around;
  }
}

.stat-item {
  text-align: center;
  min-width: 60px;
}

.stat-value {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
  margin-bottom: $spacing-xs;
}

.stat-label {
  font-size: $font-size-xs;
  color: var(--muted-foreground);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: var(--border);
}

.greeting-title {
  font-size: $font-size-3xl;
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: $spacing-sm;
  line-height: 1.2;
}

.greeting-subtitle {
  font-size: $font-size-lg;
  color: var(--muted-foreground);
  font-weight: 400;
}

// 测试进度区域
.test-progress-section {
  margin-bottom: $spacing-xl;
  background: linear-gradient(135deg, 
    oklch(from var(--primary) l c h / 0.08) 0%, 
    oklch(from var(--secondary) l c h / 0.05) 100%);
  border-radius: var(--radius-lg);
  padding: $spacing-xl;
  border: 1px solid oklch(from var(--primary) l c h / 0.15);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  @media (max-width: $mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }
}

.progress-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.progress-text {
  font-size: $font-size-sm;
  font-weight: 600;
  color: var(--primary);
  min-width: 100px;
}

.progress-bar {
  width: 200px;
  height: 8px;
  background: var(--muted);
  border-radius: 4px;
  overflow: hidden;
  
  @media (max-width: $mobile) {
    width: 150px;
  }
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-description {
  p {
    color: var(--muted-foreground);
    line-height: 1.6;
    margin-bottom: $spacing-sm;
    
    &.highlight-text {
      color: var(--primary);
      font-weight: 500;
      margin-bottom: 0;
    }
  }
}

// 手风琴式测试套件
.test-suites-accordion {
  margin-bottom: $spacing-2xl;
}

.suite-accordion-item {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  margin-bottom: $spacing-lg;
  overflow: visible;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--shadow-lg);
    border-color: oklch(from var(--primary) l c h / 0.3);
  }
}

.suite-header {
  padding: $spacing-xl; // 增加内边距以适应平板触摸
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px; // 确保最小触摸目标大小
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;

  // 添加触摸反馈效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover {
    background: var(--muted);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    transform: translateY(0);

    &::before {
      opacity: 0.05;
    }
  }

  // 确保内容在触摸反馈层之上
  > * {
    position: relative;
    z-index: 1;
  }
}

.suite-main-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-md;
  
  @media (max-width: $mobile) {
    flex-direction: column;
    gap: $spacing-md;
  }
}

.suite-title-section {
  flex: 1;
}

.suite-name {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-xs;
}

.suite-meta {
  display: flex;
  gap: $spacing-lg;
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  
  @media (max-width: $mobile) {
    flex-direction: column;
    gap: $spacing-xs;
  }
}

.suite-number,
.suite-date {
  font-weight: 500;
}

.suite-actions {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.suite-start-btn {
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  padding: $spacing-sm $spacing-lg;
  border-radius: var(--radius);
  font-size: $font-size-sm;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
  
  &:disabled {
    background: var(--muted);
    color: var(--muted-foreground);
    cursor: not-allowed;
  }
}

.suite-report-btn {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: none;
  padding: $spacing-sm $spacing-lg;
  border-radius: var(--radius);
  font-size: $font-size-sm;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;

  &:hover {
    background: oklch(from var(--secondary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.suite-expand-btn {
  background: var(--muted);
  color: var(--muted-foreground);
  border: none;
  width: 56px; // 增加按钮大小以适应平板触摸
  height: 56px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  // 添加触摸反馈效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary);
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    border-radius: 50%;
  }

  &:hover {
    background: var(--primary);
    color: var(--primary-foreground);
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
  }

  &:active {
    transform: scale(1.05);

    &::before {
      opacity: 0.2;
      transform: scale(1);
    }
  }

  &.expanded {
    transform: rotate(180deg) scale(1.1);
    background: var(--primary);
    color: var(--primary-foreground);
    box-shadow: var(--shadow-lg);
  }

  // 确保图标在触摸反馈层之上
  svg {
    position: relative;
    z-index: 1;
  }
}

.suite-progress {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.suite-progress-bar {
  flex: 1;
  height: 8px;
  background: var(--muted);
  border-radius: 4px;
  overflow: hidden;
}

.suite-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 4px;
  transition: width 0.8s ease;
}

.suite-progress-text {
  font-size: $font-size-sm;
  font-weight: 600;
  color: var(--primary);
  min-width: 80px;
}

// 测试任务列表
.test-tasks {
  margin-bottom: $spacing-2xl;
}

.suite-content {
  border-top: 1px solid var(--border);
  background: oklch(from var(--muted) l c h / 0.3);
}

.tasks-list {
  padding: $spacing-md;
}

.task-accordion-item {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  margin-bottom: $spacing-md;
  overflow: visible;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.task-header {
  padding: $spacing-lg $spacing-xl; // 增加内边距以适应平板触摸
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  min-height: 72px; // 确保最小触摸目标大小
  position: relative;

  // 添加触摸反馈效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover {
    background: var(--muted);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  &:active {
    transform: translateY(0);

    &::before {
      opacity: 0.05;
    }
  }

  // 确保内容在触摸反馈层之上
  > * {
    position: relative;
    z-index: 1;
  }
}

.task-basic-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
}

.task-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: oklch(from var(--primary) l c h / 0.1);
  border-radius: var(--radius);
  flex-shrink: 0;
}

.task-main {
  flex: 1;
}

.task-name {
  font-size: $font-size-base;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-xs;
}

.task-brief {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  flex-wrap: wrap;
}

.task-type,
.task-duration {
  background: var(--muted);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
}

.task-difficulty-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
  
  &.easy {
    background: oklch(from var(--primary) l c h / 0.1);
    color: var(--primary);
  }
  
  &.medium {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }
  
  &.hard {
    background: oklch(from var(--destructive) l c h / 0.1);
    color: var(--destructive);
  }
}

.task-status-section {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.task-completion-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.task-completion-status {
  font-size: $font-size-sm;
  font-weight: 500;
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);

  &:contains('已完成') {
    background: oklch(from var(--primary) l c h / 0.1);
    color: var(--primary);
  }

  &:contains('进行中') {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }

  &:contains('未开始') {
    background: var(--muted);
    color: var(--muted-foreground);
  }
}

.task-completion-rate {
  font-size: 11px;
  font-weight: 600;
  color: var(--muted-foreground);

  &:contains('100%') {
    color: var(--primary);
  }
}

.task-expand-btn {
  background: var(--muted);
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 48px; // 增加按钮大小以适应平板触摸
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  // 添加触摸反馈效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary);
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    border-radius: 50%;
  }

  &:hover {
    color: var(--primary);
    background: oklch(from var(--primary) l c h / 0.1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(1.05);

    &::before {
      opacity: 0.2;
      transform: scale(1);
    }
  }

  &.expanded {
    transform: rotate(180deg) scale(1.1);
    color: var(--primary);
    background: oklch(from var(--primary) l c h / 0.1);
  }

  // 确保图标在触摸反馈层之上
  svg {
    position: relative;
    z-index: 1;
  }
}

.task-details {
  padding: $spacing-lg;
  border-top: 1px solid var(--border);
  background: oklch(from var(--muted) l c h / 0.2);
}

.task-description-section,
.task-instructions {
  margin-bottom: $spacing-lg;
  
  h5 {
    font-size: $font-size-sm;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: $spacing-sm;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.task-full-description {
  color: var(--muted-foreground);
  line-height: 1.6;
  margin: 0;
}

.instructions-list {
  margin: 0;
  padding-left: $spacing-lg;
  
  li {
    color: var(--muted-foreground);
    line-height: 1.5;
    margin-bottom: $spacing-xs;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.task-detail-actions {
  display: flex;
  gap: $spacing-md;
  
  @media (max-width: $mobile) {
    flex-direction: column;
  }
}

.task-start-btn,
.task-report-btn {
  padding: $spacing-md $spacing-xl; // 增加内边距以适应平板触摸
  border-radius: 16px; // 增加圆角
  font-size: $font-size-base; // 增加字体大小
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-height: 56px; // 确保最小触摸目标大小
  position: relative;
  overflow: hidden;

  // 添加触摸反馈效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:active {
    transform: translateY(1px);

    &::before {
      opacity: 0.2;
    }
  }

  // 确保文字在触摸反馈层之上
  span {
    position: relative;
    z-index: 1;
  }
}

.task-start-btn {
  background: var(--primary);
  color: var(--primary-foreground);

  &:hover:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.1) c h);
    transform: translateY(-2px); // 增加悬停效果
    box-shadow: var(--shadow-lg);
  }

  &:disabled {
    background: var(--muted);
    color: var(--muted-foreground);
    cursor: not-allowed;
    transform: none;
  }
}

.task-report-btn {
  background: var(--secondary);
  color: var(--secondary-foreground);

  &:hover {
    background: oklch(from var(--secondary) calc(l - 0.1) c h);
    transform: translateY(-2px); // 增加悬停效果
    box-shadow: var(--shadow-lg);
  }
}

.retest-notice {
  background: linear-gradient(135deg, 
    oklch(from var(--primary) l c h / 0.05) 0%, 
    oklch(from var(--secondary) l c h / 0.03) 100%);
  border: 1px solid oklch(from var(--primary) l c h / 0.1);
  border-radius: var(--radius-lg);
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.notice-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.notice-text {
  flex: 1;
  
  h3 {
    color: var(--primary);
    font-size: $font-size-lg;
    font-weight: 600;
    margin-bottom: $spacing-xs;
  }
  
  p {
    color: var(--muted-foreground);
    font-size: $font-size-sm;
    line-height: 1.5;
    margin: 0;
  }
}

.task-count {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  background: var(--muted);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
}

.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-lg;
  
  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }
}

.task-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: $spacing-lg;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }
  
  &:hover:not(.task-unavailable) {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
    transform: translateY(-4px);
    
    &::before {
      transform: scaleX(1);
    }
  }
  
  &.task-unavailable {
    opacity: 0.7;
    background: var(--muted);
    
    .task-title,
    .task-description {
      color: var(--muted-foreground);
    }
  }
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-md;
}

.task-icon {
  font-size: 32px;
  line-height: 1;
}

.task-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: $spacing-xs;
}

.task-category {
  font-size: $font-size-xs;
  color: var(--muted-foreground);
  background: var(--muted);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
}

.task-difficulty {
  font-size: $font-size-xs;
  font-weight: 500;
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  
  &.easy {
    background: oklch(from var(--primary) l c h / 0.1);
    color: var(--primary);
  }
  
  &.medium {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }
  
  &.hard {
    background: oklch(from var(--destructive) l c h / 0.1);
    color: var(--destructive);
  }
}

.task-content {
  flex: 1;
  margin-bottom: $spacing-lg;
}

.task-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-sm;
}

.task-description {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  line-height: 1.5;
  margin-bottom: $spacing-md;
}

.task-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.task-status-badge {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
  
  &.unavailable {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }
  
  svg {
    width: 12px;
    height: 12px;
  }
}

.task-duration {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-xs;
  color: var(--muted-foreground);
  
  svg {
    width: 14px;
    height: 14px;
  }
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.start-test-btn {
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  padding: $spacing-sm $spacing-lg;
  border-radius: var(--radius);
  font-size: $font-size-sm;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover:not(:disabled) {
    background: oklch(from var(--primary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    
    &::before {
      left: 100%;
    }
  }
  
  &:disabled,
  &.btn-disabled {
    background: var(--muted);
    color: var(--muted-foreground);
    cursor: not-allowed;
    
    &::before {
      display: none;
    }
  }
}

.section-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-lg;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
}

.view-all-link {
  color: var(--primary);
  text-decoration: none;
  font-size: $font-size-sm;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  transition: all 0.2s ease;
  
  &:hover {
    color: oklch(from var(--primary) calc(l - 0.1) c h);
    text-decoration: none;
    transform: translateX(2px);
  }
  
  svg {
    transition: transform 0.2s ease;
  }
  
  &:hover svg {
    transform: translateX(2px);
  }
}


// 测试记录列表
.test-records {
  margin-bottom: $spacing-2xl;
}

.records-list {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: visible;
}

// 手风琴式测试记录
.records-accordion {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.record-date-group {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: visible;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--shadow-md);
    border-color: oklch(from var(--primary) l c h / 0.3);
  }
}

.date-header {
  padding: $spacing-lg;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, 
    oklch(from var(--primary) l c h / 0.03) 0%, 
    oklch(from var(--secondary) l c h / 0.02) 100%);
  transition: background-color 0.2s ease;
  
  &:hover {
    background: linear-gradient(135deg, 
      oklch(from var(--primary) l c h / 0.08) 0%, 
      oklch(from var(--secondary) l c h / 0.05) 100%);
  }
}

.date-info {
  flex: 1;
}

.date-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-xs;
}

.date-count {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  background: var(--muted);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
}

.date-stats {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
}

.task-report-btn {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: none;
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius);
  font-size: $font-size-xs;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
  white-space: nowrap;

  &:hover {
    background: oklch(from var(--secondary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }
}

.completion-rate {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xs;
}

.rate-text {
  font-size: $font-size-xs;
  color: var(--muted-foreground);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.rate-value {
  font-size: $font-size-lg;
  font-weight: 700;
  color: var(--primary);
}

.date-expand-btn {
  background: var(--muted);
  color: var(--muted-foreground);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: var(--primary);
    color: var(--primary-foreground);
  }
  
  &.expanded {
    transform: rotate(180deg);
    background: var(--primary);
    color: var(--primary-foreground);
  }
}

.date-records {
  border-top: 1px solid var(--border);
  background: oklch(from var(--muted) l c h / 0.2);
}

.record-accordion-item {
  border-bottom: 1px solid var(--border);
  
  &:last-child {
    border-bottom: none;
  }
}

.record-header {
  padding: $spacing-md $spacing-lg;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: var(--muted);
  }
}

.record-basic-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
}

.record-icon {
  width: 40px;
  height: 40px;
  background: oklch(from var(--primary) l c h / 0.1);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.record-main-info {
  flex: 1;
}

.record-name {
  font-size: $font-size-base;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-xs;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  flex-wrap: wrap;
}

.record-time,
.record-duration,
.record-score-preview {
  background: var(--muted);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
}

.record-score-preview {
  background: oklch(from var(--primary) l c h / 0.1);
  color: var(--primary);
  font-weight: 600;
}

.record-status-section {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.record-status-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
  
  &.status-completed {
    background: oklch(from var(--primary) l c h / 0.1);
    color: var(--primary);
  }
  
  &.status-inProgress {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }
  
  &.status-pending {
    background: var(--muted);
    color: var(--muted-foreground);
  }
  
  &.status-failed {
    background: oklch(from var(--destructive) l c h / 0.1);
    color: var(--destructive);
  }
}

.record-expand-btn {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: var(--primary);
  }
  
  &.expanded {
    transform: rotate(180deg);
    color: var(--primary);
  }
}

.record-details {
  padding: $spacing-lg;
  border-top: 1px solid var(--border);
  background: var(--card);
}

.record-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
  
  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-sm;
  background: oklch(from var(--muted) l c h / 0.3);
  border-radius: var(--radius);
}

.summary-label {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  font-weight: 500;
}

.summary-value {
  font-size: $font-size-sm;
  color: var(--foreground);
  font-weight: 600;
  
  &.score-highlight {
    color: var(--primary);
    font-size: $font-size-base;
  }
}

.record-actions {
  display: flex;
  gap: $spacing-md;
  flex-wrap: wrap;
  
  @media (max-width: $mobile) {
    flex-direction: column;
  }
}

.view-result-btn,
.retest-btn,
.continue-btn {
  padding: $spacing-sm $spacing-lg;
  border-radius: var(--radius);
  font-size: $font-size-sm;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.view-result-btn {
  background: var(--primary);
  color: var(--primary-foreground);

  &:hover {
    background: oklch(from var(--primary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.retest-btn {
  background: var(--secondary);
  color: var(--secondary-foreground);
  
  &:hover {
    background: oklch(from var(--secondary) calc(l - 0.1) c h);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.continue-btn {
  background: oklch(from var(--primary) l c h / 0.1);
  color: var(--primary);
  border: 1px solid var(--primary);
  
  &:hover {
    background: var(--primary);
    color: var(--primary-foreground);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.record-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-lg;
  border-bottom: 1px solid var(--border);
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: var(--muted);
    transform: translateX(4px);
  }
  
  &:focus {
    outline: 2px solid var(--primary);
    outline-offset: -2px;
    background: var(--muted);
  }
}

.record-icon {
  width: 48px;
  height: 48px;
  background: var(--muted);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.record-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  @media (max-width: $mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.record-main {
  flex: 1;
}

.record-name {
  font-size: $font-size-base;
  font-weight: 500;
  color: var(--foreground);
  margin-bottom: $spacing-xs;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  font-size: $font-size-sm;
  color: var(--muted-foreground);
}

.record-date,
.record-duration {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
}

.record-result {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  flex-shrink: 0;
}

.status-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
  
  &.status-completed {
    background: oklch(from var(--primary) l c h / 0.1);
    color: var(--primary);
  }
  
  &.status-inProgress {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }
  
  &.status-pending {
    background: var(--muted);
    color: var(--muted-foreground);
  }
  
  &.status-failed {
    background: oklch(from var(--destructive) l c h / 0.1);
    color: var(--destructive);
  }
}

.record-score {
  font-weight: 600;
  color: var(--primary);
  font-size: $font-size-sm;
}

.empty-records {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: $spacing-2xl;
  text-align: center;
  
  .start-first-test-btn {
    margin-top: $spacing-lg;
    background: var(--primary);
    color: var(--primary-foreground);
    border: none;
    padding: $spacing-sm $spacing-lg;
    border-radius: var(--radius);
    font-size: $font-size-sm;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: oklch(from var(--primary) calc(l - 0.1) c h);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: $spacing-md;
  opacity: 0.5;
}

.empty-text {
  font-size: $font-size-lg;
  font-weight: 500;
  color: var(--foreground);
  margin-bottom: $spacing-xs;
}

.empty-hint {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
}

.record-arrow {
  color: var(--muted-foreground);
  transition: all 0.2s ease;
  opacity: 0.7;
  
  .record-item:hover & {
    color: var(--primary);
    opacity: 1;
    transform: translateX(4px);
  }
}

// 响应式设计
@media (max-width: $tablet) {
  .main-content {
    padding: $spacing-lg $spacing-md;
  }
  
  .greeting-title {
    font-size: $font-size-2xl;
  }
  
  .tasks-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: $mobile) {
  .main-content {
    padding: $spacing-md;
  }
  
  .greeting-title {
    font-size: $font-size-xl;
  }
  
  .nav-content {
    padding: $spacing-sm $spacing-md;
  }
  
  .user-name {
    font-size: $font-size-base;
  }
  
  .record-item {
    padding: $spacing-md;
    
    &:hover {
      transform: none;
    }
  }
  
  .record-arrow {
    display: none;
  }
}

// 测试报告模态框样式
.report-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  animation: fadeIn 0.3s ease;
}

.report-modal {
  background: var(--card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-width: 1280px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;

  @media (max-width: $tablet) {
    max-width: 95vw;
    max-height: 85vh;
  }
}

.report-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-xl $spacing-xl $spacing-lg;
  border-bottom: 1px solid var(--border);
  background: linear-gradient(135deg,
    oklch(from var(--primary) l c h / 0.05) 0%,
    oklch(from var(--secondary) l c h / 0.03) 100%);
}

.report-modal-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
}

.report-modal-close {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: $spacing-sm;
  border-radius: var(--radius);
  transition: all 0.2s ease;

  &:hover {
    background: var(--muted);
    color: var(--foreground);
  }
}

.report-modal-content {
  padding: $spacing-xl;
  overflow-y: auto;
  max-height: calc(90vh - 200px);

  @media (max-width: $tablet) {
    padding: $spacing-lg;
    max-height: calc(85vh - 180px);
  }
}

.report-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-lg;
  margin-bottom: $spacing-xl;

  // 当有3个指标时，调整布局
  &:has(.attempts-card) {
    grid-template-columns: 1fr 1fr 1fr;

    @media (max-width: $tablet) {
      grid-template-columns: 1fr 1fr;

      .attempts-card {
        grid-column: 1 / -1;
      }
    }
  }

  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }
}

.metric-card {
  background: linear-gradient(135deg,
    oklch(from var(--primary) l c h / 0.08) 0%,
    oklch(from var(--secondary) l c h / 0.05) 100%);
  border: 1px solid oklch(from var(--primary) l c h / 0.1);
  border-radius: var(--radius-lg);
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.metric-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.metric-info {
  flex: 1;
}

.metric-label {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  margin-bottom: $spacing-xs;
}

.metric-value {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--primary);
}

.report-section {
  margin-bottom: $spacing-xl;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-md;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.interpretation-text {
  color: var(--muted-foreground);
  line-height: 1.6;
  font-size: $font-size-base;
  background: var(--muted);
  padding: $spacing-lg;
  border-radius: var(--radius);
  border-left: 4px solid var(--primary);
}

.comparison-card {
  background: var(--muted);
  border-radius: var(--radius);
  padding: $spacing-lg;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.comparison-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: $mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }
}

.comparison-label {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
  font-weight: 500;
}

.comparison-status {
  font-weight: 600;
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-sm;

  &.above-average {
    background: oklch(from var(--primary) l c h / 0.1);
    color: var(--primary);
  }

  &.below-average {
    background: oklch(from var(--secondary) l c h / 0.1);
    color: var(--secondary);
  }
}

.comparison-value {
  font-weight: 600;
  color: var(--foreground);
}

.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.recommendation-item {
  background: var(--muted);
  padding: $spacing-md;
  border-radius: var(--radius);
  border-left: 4px solid var(--secondary);
  color: var(--muted-foreground);
  line-height: 1.5;
  position: relative;

  &::before {
    content: '💡';
    position: absolute;
    left: -2px;
    top: -2px;
    background: var(--card);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 2px solid var(--secondary);
  }

  padding-left: $spacing-xl;
}

.report-modal-footer {
  padding: $spacing-lg $spacing-xl;
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: flex-end;
  background: var(--muted);
}

.modal-btn {
  border: none;
  padding: $spacing-md $spacing-xl;
  border-radius: var(--radius);
  font-size: $font-size-base;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &.modal-btn-primary {
    background: var(--primary);
    color: var(--primary-foreground);

    &:hover {
      background: oklch(from var(--primary) calc(l - 0.1) c h);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 平板端专用优化 (768px - 1024px)
@media (min-width: $mobile) and (max-width: $tablet) {
  .suite-header {
    padding: $spacing-2xl; // 平板端增加更多内边距
    min-height: 96px;
  }

  .suite-expand-btn {
    width: 64px;
    height: 64px;
  }

  .task-header {
    padding: $spacing-xl $spacing-2xl;
    min-height: 84px;
  }

  .task-expand-btn {
    width: 56px;
    height: 56px;
  }

  .task-start-btn,
  .task-report-btn {
    padding: $spacing-lg $spacing-2xl;
    min-height: 64px;
    font-size: $font-size-lg;
  }

  .suite-start-btn {
    padding: $spacing-md $spacing-2xl;
    font-size: $font-size-lg;
    min-height: 56px;
  }

  .task-icon {
    width: 56px;
    height: 56px;
    font-size: 28px;
  }

  .task-name {
    font-size: $font-size-lg;
  }

  .suite-name {
    font-size: $font-size-2xl;
  }
}

// 平板端通用优化
@media (max-width: $tablet) {
  .main-content {
    padding: $spacing-xl $spacing-lg;
  }

  .greeting-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-lg;
  }

  .quick-stats {
    align-self: stretch;
    justify-content: space-around;
  }

  .report-modal-overlay {
    padding: $spacing-md;
  }

  .report-modal {
    border-radius: var(--radius-lg);
  }

  .report-modal-header {
    padding: $spacing-lg;
  }

  .report-modal-title {
    font-size: $font-size-lg;
  }

  .metric-card {
    padding: $spacing-md;
  }

  .metric-icon {
    font-size: 1.5rem;
  }

  .metric-value {
    font-size: $font-size-xl;
  }

  .modal-btn {
    min-width: 100px;
    padding: $spacing-sm $spacing-lg;
  }
}

// 移动端优化
@media (max-width: $mobile) {
  .report-modal-overlay {
    padding: $spacing-sm;
    align-items: flex-end;
  }

  .report-modal {
    max-height: 95vh;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }

  .report-modal-content {
    max-height: calc(95vh - 160px);
  }

  .report-metrics {
    grid-template-columns: 1fr;
  }

  .comparison-item {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }

  .recommendation-item {
    padding: $spacing-sm $spacing-md;
    padding-left: $spacing-lg;

    &::before {
      width: 20px;
      height: 20px;
      font-size: 10px;
    }
  }
}
</style>