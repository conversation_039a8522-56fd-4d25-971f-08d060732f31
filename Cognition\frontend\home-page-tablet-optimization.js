/**
 * 主页卡片平板横屏优化脚本
 * 专门针对平板设备横屏模式下的主页卡片布局优化
 */

(function() {
    'use strict';
    
    console.log('🏠 主页平板优化脚本开始加载...');
    
    // 检测设备和屏幕方向
    function detectTabletLandscape() {
        const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1024;
        const isLandscape = window.innerWidth > window.innerHeight;
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        return {
            isTablet,
            isLandscape,
            isTouchDevice,
            shouldOptimize: isTablet && isLandscape && isTouchDevice
        };
    }
    
    // 添加主页卡片优化CSS
    function addHomePageOptimizationCSS() {
        const cssId = 'home-page-tablet-optimization';
        
        // 如果已存在则移除
        const existingStyle = document.getElementById(cssId);
        if (existingStyle) {
            existingStyle.remove();
        }
        
        const css = `
        /* 主页平板横屏优化样式 */
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
            /* 主容器优化 */
            .main-content {
                padding: 24px 32px !important;
                max-width: 100% !important;
            }
            
            /* 测试套件卡片网格布局 */
            .test-suites-accordion {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)) !important;
                gap: 24px !important;
                margin-bottom: 32px !important;
            }
            
            .suite-accordion-item {
                width: 100% !important;
                max-width: none !important;
                margin-bottom: 0 !important;
                background: var(--card) !important;
                border: 1px solid var(--border) !important;
                border-radius: 16px !important;
                overflow: hidden !important;
                transition: all 0.3s ease !important;
            }
            
            .suite-accordion-item:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
                border-color: var(--primary) !important;
            }
            
            /* 套件头部优化 */
            .suite-header {
                padding: 24px !important;
                min-height: 120px !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: space-between !important;
            }
            
            .suite-main-info {
                flex-direction: column !important;
                gap: 16px !important;
                align-items: flex-start !important;
                margin-bottom: 16px !important;
            }
            
            .suite-title-section {
                width: 100% !important;
            }
            
            .suite-name {
                font-size: 20px !important;
                font-weight: 600 !important;
                margin-bottom: 8px !important;
                color: var(--foreground) !important;
            }
            
            .suite-meta {
                display: flex !important;
                gap: 16px !important;
                font-size: 14px !important;
                color: var(--muted-foreground) !important;
            }
            
            /* 套件操作区域 */
            .suite-actions {
                display: flex !important;
                align-items: center !important;
                justify-content: space-between !important;
                width: 100% !important;
                gap: 16px !important;
            }
            
            .suite-start-btn {
                flex: 1 !important;
                max-width: 200px !important;
                min-height: 56px !important;
                font-size: 16px !important;
                font-weight: 500 !important;
                padding: 16px 24px !important;
                border-radius: 12px !important;
                background: var(--primary) !important;
                color: var(--primary-foreground) !important;
                border: none !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
            }
            
            .suite-start-btn:hover:not(:disabled) {
                background: var(--primary-hover) !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }
            
            .suite-start-btn:active {
                transform: translateY(0) scale(0.98) !important;
            }
            
            .suite-expand-btn {
                width: 56px !important;
                height: 56px !important;
                border-radius: 50% !important;
                background: var(--muted) !important;
                color: var(--muted-foreground) !important;
                border: none !important;
                cursor: pointer !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                transition: all 0.3s ease !important;
                flex-shrink: 0 !important;
            }
            
            .suite-expand-btn:hover {
                background: var(--primary) !important;
                color: var(--primary-foreground) !important;
                transform: scale(1.1) !important;
            }
            
            .suite-expand-btn.expanded {
                transform: rotate(180deg) scale(1.1) !important;
                background: var(--primary) !important;
                color: var(--primary-foreground) !important;
            }
            
            /* 进度条优化 */
            .suite-progress {
                display: flex !important;
                align-items: center !important;
                gap: 12px !important;
                margin-top: 16px !important;
            }
            
            .suite-progress-bar {
                flex: 1 !important;
                height: 8px !important;
                background: var(--muted) !important;
                border-radius: 4px !important;
                overflow: hidden !important;
            }
            
            .suite-progress-fill {
                height: 100% !important;
                background: linear-gradient(90deg, var(--primary), var(--secondary)) !important;
                border-radius: 4px !important;
                transition: width 0.8s ease !important;
            }
            
            .suite-progress-text {
                font-size: 14px !important;
                font-weight: 600 !important;
                color: var(--primary) !important;
                min-width: 80px !important;
                text-align: right !important;
            }
            
            /* 任务列表网格布局 */
            .tasks-list {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
                gap: 16px !important;
                padding: 24px !important;
            }
            
            .task-accordion-item {
                margin-bottom: 0 !important;
                background: var(--card) !important;
                border: 1px solid var(--border) !important;
                border-radius: 12px !important;
                overflow: hidden !important;
                transition: all 0.3s ease !important;
            }
            
            .task-accordion-item:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            }
            
            .task-header {
                padding: 20px !important;
                min-height: 80px !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }
            
            .task-basic-info {
                display: flex !important;
                align-items: center !important;
                gap: 16px !important;
                flex: 1 !important;
            }
            
            .task-icon {
                width: 56px !important;
                height: 56px !important;
                font-size: 28px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                background: var(--muted) !important;
                border-radius: 12px !important;
                flex-shrink: 0 !important;
            }
            
            .task-name {
                font-size: 16px !important;
                font-weight: 600 !important;
                margin-bottom: 4px !important;
            }
            
            .task-brief {
                display: flex !important;
                gap: 12px !important;
                font-size: 14px !important;
                color: var(--muted-foreground) !important;
            }
            
            /* 历史记录网格布局 */
            .records-accordion {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
                gap: 20px !important;
            }
            
            .record-date-group {
                margin-bottom: 0 !important;
                background: var(--card) !important;
                border: 1px solid var(--border) !important;
                border-radius: 12px !important;
                overflow: hidden !important;
            }
            
            .date-header {
                padding: 20px !important;
                min-height: 72px !important;
                background: var(--muted) !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
            }
            
            .date-header:hover {
                background: var(--primary) !important;
                color: var(--primary-foreground) !important;
            }
            
            .date-records {
                padding: 16px !important;
            }
            
            .record-accordion-item {
                margin-bottom: 12px !important;
                border: 1px solid var(--border) !important;
                border-radius: 8px !important;
                overflow: hidden !important;
            }
            
            .record-accordion-item:last-child {
                margin-bottom: 0 !important;
            }
            
            .record-header {
                padding: 16px !important;
                min-height: 64px !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
            }
            
            .record-header:hover {
                background: var(--muted) !important;
            }
        }
        `;
        
        const style = document.createElement('style');
        style.id = cssId;
        style.textContent = css;
        document.head.appendChild(style);
        
        console.log('✅ 主页卡片优化CSS已注入');
    }
    
    // 应用JavaScript优化
    function applyJavaScriptOptimizations() {
        const device = detectTabletLandscape();
        
        if (!device.shouldOptimize) {
            console.log('⚠️ 当前设备不需要主页卡片优化');
            return false;
        }
        
        let optimizedCount = 0;
        
        // 优化测试套件容器
        const testSuitesContainer = document.querySelector('.test-suites-accordion');
        if (testSuitesContainer) {
            testSuitesContainer.style.display = 'grid';
            testSuitesContainer.style.gridTemplateColumns = 'repeat(auto-fit, minmax(450px, 1fr))';
            testSuitesContainer.style.gap = '24px';
            optimizedCount++;
        }
        
        // 优化历史记录容器
        const recordsContainer = document.querySelector('.records-accordion');
        if (recordsContainer) {
            recordsContainer.style.display = 'grid';
            recordsContainer.style.gridTemplateColumns = 'repeat(auto-fit, minmax(400px, 1fr))';
            recordsContainer.style.gap = '20px';
            optimizedCount++;
        }
        
        // 优化任务列表容器
        const taskLists = document.querySelectorAll('.tasks-list');
        taskLists.forEach(taskList => {
            taskList.style.display = 'grid';
            taskList.style.gridTemplateColumns = 'repeat(auto-fit, minmax(320px, 1fr))';
            taskList.style.gap = '16px';
            optimizedCount++;
        });
        
        console.log(`✅ JavaScript优化完成，优化了 ${optimizedCount} 个容器`);
        return optimizedCount > 0;
    }
    
    // 主优化函数
    function optimizeHomePage() {
        console.log('🔍 检测设备和屏幕方向...');
        const device = detectTabletLandscape();
        
        console.log('📱 设备信息:', device);
        
        if (!device.shouldOptimize) {
            console.log('⚠️ 当前设备不需要主页优化');
            return false;
        }
        
        // 注入CSS
        addHomePageOptimizationCSS();
        
        // 应用JavaScript优化
        const jsOptimized = applyJavaScriptOptimizations();
        
        console.log('🎉 主页平板优化完成！');
        return true;
    }
    
    // 监听窗口大小和方向变化
    let resizeTimeout;
    function handleResize() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            console.log('📐 屏幕方向或大小变化，重新优化...');
            optimizeHomePage();
        }, 300);
    }
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    
    // 导出到全局
    window.HomePageTabletOptimizer = {
        optimize: optimizeHomePage,
        detect: detectTabletLandscape,
        addCSS: addHomePageOptimizationCSS,
        applyJS: applyJavaScriptOptimizations
    };
    
    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', optimizeHomePage);
    } else {
        optimizeHomePage();
    }
    
    console.log('🚀 主页平板优化脚本加载完成');
    console.log('💡 使用 HomePageTabletOptimizer.optimize() 手动触发优化');
    
})();
