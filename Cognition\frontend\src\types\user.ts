// 用户相关类型定义

export interface User {
  id: string
  patientNumber: string
  phone?: string
  name: string
  education: string
  gender: 'male' | 'female' | 'other'
  contactPhone: string
  dominantHand: 'left' | 'right'
  isColorBlind: boolean
  idCard?: string
  avatar?: string
  createdAt: string
  lastLoginAt?: string
}

export interface LoginCredentials {
  type: 'phone' | 'patientNumber' | 'qrcode'
  value: string
  password?: string
  verifyCode?: string
}

export interface RegisterData {
  name: string
  education: string
  gender: 'male' | 'female' | 'other'
  contactPhone: string
  dominantHand: 'left' | 'right'
  isColorBlind: boolean
  idCard?: string
  phone?: string
  password?: string
}

export interface TestRecord {
  id: string
  userId: string
  testType: string
  testName: string
  startTime: string
  endTime?: string
  status: 'pending' | 'inProgress' | 'completed' | 'failed'
  score?: number
  duration?: number
  results?: {
    [key: string]: any
    overallPercentile?: number
    recommendations?: string[]
  }
  isRetesting?: boolean // 是否为重测
}

export interface TestReport {
  id: string
  testName: string
  testType: string
  completedAt?: string
  duration?: number
  score?: number
  percentile: number
  detailedResults: Record<string, any>
  recommendations: string[]
  interpretation: string
  comparisonData: {
    userScore: number
    averageScore: number
    difference: number
    percentageDiff: number
    isAboveAverage: boolean
  }
}