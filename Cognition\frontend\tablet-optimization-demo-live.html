<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平板端实时优化演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 28px;
            color: #667eea;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
        }

        .control-panel h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .optimize-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .optimize-btn:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .reset-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .reset-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .demo-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
        }

        .demo-section h3 {
            margin-bottom: 20px;
            color: #495057;
        }

        .register-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            data-selected: true;
        }

        .register-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .register-card h4 {
            margin-bottom: 15px;
            font-size: 24px;
        }

        .register-card p {
            opacity: 0.9;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .demo-button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .primary-btn {
            background: #007bff;
            color: white;
        }

        .primary-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .secondary-btn {
            background: #6c757d;
            color: white;
        }

        .secondary-btn:hover {
            background: #545b62;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .success-btn {
            background: #28a745;
            color: white;
        }

        .success-btn:hover {
            background: #1e7e34;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .list-demo {
            list-style: none;
        }

        .list-item {
            background: #f8f9fa;
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .list-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .status-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid #dee2e6;
            max-width: 300px;
            z-index: 1000;
        }

        .status-panel h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .status-value {
            font-weight: 600;
            color: #28a745;
        }

        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .status-panel {
                position: relative;
                top: auto;
                right: auto;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h1>🎯 平板端实时优化演示</h1>
            <p>点击下方按钮来优化页面元素，适配平板端触摸操作</p>
        </div>

        <div class="control-panel">
            <h3>🛠️ 优化控制面板</h3>
            <button class="optimize-btn" onclick="runOptimization()">
                🚀 执行平板端优化
            </button>
            <button class="reset-btn" onclick="resetOptimization()">
                🔄 重置优化
            </button>
            <button class="optimize-btn" onclick="toggleDeviceSimulation()">
                📱 切换设备模拟
            </button>
            <div class="console-output" id="consoleOutput">
                等待执行优化脚本...
            </div>
        </div>

        <div class="demo-section">
            <h3>📋 注册卡片 (选中元素)</h3>
            <div class="register-card" data-selected="true" onclick="selectElement(this)">
                <h4>🎓 认知测试注册</h4>
                <p>欢迎参加认知能力评估测试。我们将通过一系列科学设计的测试来评估您的认知功能，包括记忆力、注意力、执行功能等多个维度。</p>
            </div>
        </div>

        <div class="demo-section">
            <h3>📝 表单元素</h3>
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="email">邮箱地址</label>
                <input type="email" id="email" placeholder="请输入邮箱地址">
            </div>
            <div class="form-group">
                <label for="phone">手机号码</label>
                <input type="tel" id="phone" placeholder="请输入手机号码">
            </div>
        </div>

        <div class="demo-section">
            <h3>🔘 按钮组合</h3>
            <div class="button-group">
                <button class="demo-button primary-btn">主要按钮</button>
                <button class="demo-button secondary-btn">次要按钮</button>
                <button class="demo-button success-btn">成功按钮</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>📋 列表项目</h3>
            <ul class="list-demo">
                <li class="list-item">📊 PDQ-5 帕金森病问卷</li>
                <li class="list-item">🧠 Hopkins词汇学习测试</li>
                <li class="list-item">🔄 N-back工作记忆测试</li>
                <li class="list-item">🎨 Stroop色词测试</li>
                <li class="list-item">🔗 连线测试</li>
            </ul>
        </div>
    </div>

    <div class="status-panel" id="statusPanel">
        <h4>📊 优化状态</h4>
        <div class="status-item">
            <span>设备类型:</span>
            <span class="status-value" id="deviceType">检测中...</span>
        </div>
        <div class="status-item">
            <span>屏幕尺寸:</span>
            <span class="status-value" id="screenSize">-</span>
        </div>
        <div class="status-item">
            <span>触摸支持:</span>
            <span class="status-value" id="touchSupport">-</span>
        </div>
        <div class="status-item">
            <span>优化状态:</span>
            <span class="status-value" id="optimizationStatus">未优化</span>
        </div>
        <div class="status-item">
            <span>优化元素:</span>
            <span class="status-value" id="optimizedCount">0</span>
        </div>
    </div>

    <script>
        let isOptimized = false;
        let originalStyles = new Map();

        // 更新状态面板
        function updateStatus() {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            let deviceType = '桌面设备';
            if (screenWidth >= 768 && screenWidth <= 1024) {
                deviceType = '平板设备';
            } else if (screenWidth < 768) {
                deviceType = '移动设备';
            }
            
            document.getElementById('deviceType').textContent = deviceType;
            document.getElementById('screenSize').textContent = `${screenWidth}×${screenHeight}`;
            document.getElementById('touchSupport').textContent = isTouchDevice ? '支持' : '不支持';
            document.getElementById('optimizationStatus').textContent = isOptimized ? '已优化' : '未优化';
        }

        // 输出到控制台
        function logToConsole(message, type = 'info') {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            output.innerHTML += `<div>[${timestamp}] ${prefix} ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        // 选择元素
        function selectElement(element) {
            // 移除其他元素的选中状态
            document.querySelectorAll('[data-selected="true"]').forEach(el => {
                if (el !== element) {
                    el.removeAttribute('data-selected');
                }
            });
            
            // 设置当前元素为选中状态
            element.setAttribute('data-selected', 'true');
            logToConsole(`已选择元素: ${element.tagName}.${element.className}`);
        }

        // 执行优化
        function runOptimization() {
            if (isOptimized) {
                logToConsole('优化已经执行过了', 'error');
                return;
            }

            logToConsole('开始执行平板端优化...');
            
            // 这里会加载并执行优化脚本
            const script = document.createElement('script');
            script.src = './tablet-optimization-script.js';
            script.onload = function() {
                logToConsole('优化脚本加载成功', 'success');
                isOptimized = true;
                updateOptimizedCount();
                updateStatus();
            };
            script.onerror = function() {
                logToConsole('优化脚本加载失败', 'error');
            };
            document.head.appendChild(script);
        }

        // 重置优化
        function resetOptimization() {
            logToConsole('重置优化设置...');
            
            // 移除优化样式
            const optimizationStyle = document.getElementById('tablet-optimization-styles');
            if (optimizationStyle) {
                optimizationStyle.remove();
                logToConsole('已移除优化样式', 'success');
            }
            
            // 移除优化类
            document.querySelectorAll('.tablet-optimized, .touch-feedback').forEach(el => {
                el.classList.remove('tablet-optimized', 'touch-feedback');
            });
            
            // 重置body类
            document.body.classList.remove('tablet-device', 'touch-device', 'landscape-mode', 'portrait-mode');
            
            isOptimized = false;
            updateStatus();
            logToConsole('优化已重置', 'success');
        }

        // 切换设备模拟
        function toggleDeviceSimulation() {
            const body = document.body;
            if (body.classList.contains('simulate-tablet')) {
                body.classList.remove('simulate-tablet');
                body.style.maxWidth = '';
                logToConsole('已关闭平板模拟模式');
            } else {
                body.classList.add('simulate-tablet');
                body.style.maxWidth = '1024px';
                logToConsole('已开启平板模拟模式');
            }
        }

        // 更新优化元素计数
        function updateOptimizedCount() {
            const count = document.querySelectorAll('.tablet-optimized').length;
            document.getElementById('optimizedCount').textContent = count;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            logToConsole('页面加载完成，准备进行优化');
            
            // 监听窗口大小变化
            window.addEventListener('resize', updateStatus);
            
            // 添加触摸事件监听（用于演示）
            document.addEventListener('touchstart', function() {
                logToConsole('检测到触摸事件');
            }, { passive: true });
        });

        // 手动执行优化脚本内容（内联版本）
        function runInlineOptimization() {
            logToConsole('执行内联优化脚本...');
            
            // 注入CSS样式
            const css = `
                @media (min-width: 768px) and (max-width: 1024px) {
                    button, .btn, input, textarea, select, a {
                        min-height: 56px !important;
                        padding: 16px 24px !important;
                        font-size: 18px !important;
                        border-radius: 12px !important;
                        transition: all 0.3s ease !important;
                    }
                    
                    .register-card, .demo-section {
                        padding: 32px !important;
                        border-radius: 20px !important;
                    }
                    
                    button:active, .btn:active {
                        transform: scale(0.95) !important;
                    }
                }
            `;
            
            const style = document.createElement('style');
            style.id = 'inline-tablet-optimization';
            style.textContent = css;
            document.head.appendChild(style);
            
            // 优化选中元素
            const selectedElement = document.querySelector('[data-selected="true"]');
            if (selectedElement) {
                selectedElement.style.cssText += `
                    min-height: 72px !important;
                    padding: 24px 32px !important;
                    border-radius: 16px !important;
                    transition: all 0.3s ease !important;
                `;
                selectedElement.classList.add('tablet-optimized');
                logToConsole('选中元素已优化', 'success');
            }
            
            // 批量优化
            const elements = document.querySelectorAll('button, input, .list-item');
            elements.forEach(el => el.classList.add('tablet-optimized'));
            
            isOptimized = true;
            updateOptimizedCount();
            updateStatus();
            logToConsole(`优化完成，共优化 ${elements.length} 个元素`, 'success');
        }

        // 如果外部脚本加载失败，使用内联版本
        setTimeout(() => {
            if (!isOptimized) {
                logToConsole('外部脚本未加载，使用内联优化');
                // 可以在这里调用 runInlineOptimization()
            }
        }, 2000);
    </script>
</body>
</html>
