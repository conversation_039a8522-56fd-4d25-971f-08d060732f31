<template>
  <div class="custom-select" :class="{ 'is-open': isOpen, 'is-disabled': disabled }">
    <div
      class="select-trigger"
      @click="toggleDropdown"
      @keydown.enter.prevent="toggleDropdown"
      @keydown.space.prevent="toggleDropdown"
      @keydown.escape="closeDropdown"
      @keydown.arrow-down.prevent="navigateOptions(1)"
      @keydown.arrow-up.prevent="navigateOptions(-1)"
      tabindex="0"
      :aria-expanded="isOpen"
      :aria-haspopup="true"
      role="combobox"
    >
      <span class="selected-text" :class="{ 'placeholder': !selectedOption }">
        {{ selectedOption ? selectedOption.label : placeholder }}
      </span>
      <svg class="dropdown-icon" :class="{ 'rotated': isOpen }" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="6,9 12,15 18,9"></polyline>
      </svg>
    </div>
    
    <transition name="dropdown">
      <div v-if="isOpen" class="dropdown-menu" :style="dropdownStyle">
        <div class="dropdown-content">
          <div
            v-for="(option, index) in options"
            :key="option.value"
            class="dropdown-option"
            :class="{ 
              'is-selected': option.value === modelValue,
              'is-highlighted': index === highlightedIndex
            }"
            @click="selectOption(option)"
            @mouseenter="highlightedIndex = index"
            role="option"
            :aria-selected="option.value === modelValue"
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

interface Option {
  label: string
  value: string
}

interface Props {
  modelValue: string
  options: Option[]
  placeholder?: string
  disabled?: boolean
  maxHeight?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
  disabled: false,
  maxHeight: '200px'
})

const emit = defineEmits<Emits>()

const isOpen = ref(false)
const highlightedIndex = ref(-1)

const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue)
})

const dropdownStyle = computed(() => ({
  maxHeight: props.maxHeight
}))

const toggleDropdown = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    highlightedIndex.value = props.options.findIndex(option => option.value === props.modelValue)
    nextTick(() => {
      scrollToHighlighted()
    })
  }
}

const closeDropdown = () => {
  isOpen.value = false
  highlightedIndex.value = -1
}

const selectOption = (option: Option) => {
  emit('update:modelValue', option.value)
  emit('change', option.value)
  closeDropdown()
}

const navigateOptions = (direction: number) => {
  if (!isOpen.value) {
    toggleDropdown()
    return
  }
  
  const newIndex = highlightedIndex.value + direction
  if (newIndex >= 0 && newIndex < props.options.length) {
    highlightedIndex.value = newIndex
    scrollToHighlighted()
  }
}

const scrollToHighlighted = () => {
  const dropdown = document.querySelector('.dropdown-content')
  const highlighted = document.querySelector('.dropdown-option.is-highlighted')
  if (dropdown && highlighted) {
    highlighted.scrollIntoView({ block: 'nearest' })
  }
}

const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.custom-select')) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.custom-select {
  position: relative;
  width: 100%;
}

.select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 48px;
  padding: 12px 16px;
  background: transparent;
  border: 1px solid oklch(0.88 0.005 240);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: $font-size-base;
  color: var(--foreground);
  
  &:hover {
    border-color: oklch(0.8 0.01 240);
  }
  
  &:focus {
    outline: none;
    border-color: oklch(0.65 0.15 220);
    box-shadow: 0 0 0 2px oklch(0.65 0.15 220 / 0.2);
  }
  
  .selected-text {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &.placeholder {
      color: var(--muted-foreground);
      font-style: italic;
    }
  }
  
  .dropdown-icon {
    flex-shrink: 0;
    margin-left: 8px;
    transition: transform 0.3s ease;
    color: var(--muted-foreground);
    
    &.rotated {
      transform: rotate(180deg);
    }
  }
}

.is-disabled .select-trigger {
  opacity: 0.5;
  cursor: not-allowed;
  
  &:hover {
    border-color: oklch(0.88 0.005 240);
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.dropdown-content {
  max-height: inherit;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--muted);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 3px;
    
    &:hover {
      background: var(--foreground);
    }
  }
}

.dropdown-option {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: $font-size-base;
  color: var(--foreground);
  border-bottom: 1px solid var(--border);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover,
  &.is-highlighted {
    background: var(--muted);
  }
  
  &.is-selected {
    background: var(--primary);
    color: var(--primary-foreground);
    font-weight: 500;
    
    &:hover,
    &.is-highlighted {
      background: oklch(from var(--primary) calc(l - 0.05) c h);
    }
  }
}

// 下拉动画
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
  transform-origin: top;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0.8) translateY(-8px);
}

// 平板端优化
@media (min-width: 768px) and (max-width: 1024px) {
  .select-trigger {
    min-height: 56px;
    padding: 16px 20px;
    font-size: 18px;
  }
  
  .dropdown-option {
    padding: 16px 20px;
    font-size: 18px;
    min-height: 56px;
    display: flex;
    align-items: center;
  }
}

// 触摸设备优化
@media (pointer: coarse) {
  .select-trigger {
    min-height: 56px;
    padding: 16px 20px;
    font-size: 18px;
  }
  
  .dropdown-option {
    padding: 16px 20px;
    min-height: 56px;
    font-size: 18px;
    display: flex;
    align-items: center;
  }
}

// 移动端优化
@media (max-width: 768px) {
  .select-trigger {
    min-height: 52px;
    padding: 14px 16px;
    font-size: 16px;
  }
  
  .dropdown-option {
    padding: 14px 16px;
    font-size: 16px;
    min-height: 52px;
    display: flex;
    align-items: center;
  }
}
</style>
