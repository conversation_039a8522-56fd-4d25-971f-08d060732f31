// 平板端实时优化脚本
// 在浏览器控制台中执行此脚本来优化选中的元素

(function() {
    'use strict';
    
    console.log('🚀 开始平板端优化...');
    
    // 1. 注入平板端优化CSS样式
    const optimizationCSS = `
        /* 平板端触摸优化样式 */
        <style id="tablet-optimization-styles">
        :root {
            --touch-target-min: 44px;
            --touch-target-comfortable: 56px;
            --touch-target-large: 72px;
            --tablet-spacing: 16px;
            --tablet-spacing-lg: 24px;
            --tablet-spacing-xl: 32px;
        }
        
        /* 平板端专用优化 (768px - 1024px) */
        @media (min-width: 768px) and (max-width: 1024px) {
            /* 基础交互元素优化 */
            button, .btn, [role="button"], 
            input[type="text"], input[type="email"], input[type="password"], 
            input[type="tel"], textarea, select {
                min-height: var(--touch-target-comfortable) !important;
                min-width: var(--touch-target-min) !important;
                padding: var(--tablet-spacing) var(--tablet-spacing-lg) !important;
                font-size: 18px !important;
                border-radius: 12px !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                border-width: 2px !important;
            }
            
            /* 按钮特殊优化 */
            button, .btn, [role="button"] {
                font-weight: 600 !important;
                position: relative !important;
                overflow: hidden !important;
            }
            
            /* 触摸反馈效果 */
            button::before, .btn::before, [role="button"]::before {
                content: '' !important;
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                background: rgba(255, 255, 255, 0.2) !important;
                opacity: 0 !important;
                transition: opacity 0.2s ease !important;
                pointer-events: none !important;
                z-index: 1 !important;
            }
            
            button:active::before, .btn:active::before, [role="button"]:active::before {
                opacity: 1 !important;
            }
            
            button:hover, .btn:hover, [role="button"]:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
            }
            
            button:active, .btn:active, [role="button"]:active {
                transform: translateY(0) scale(0.98) !important;
            }
            
            /* 输入框焦点优化 */
            input:focus, textarea:focus, select:focus {
                outline: 3px solid #007AFF !important;
                outline-offset: 2px !important;
                transform: scale(1.02) !important;
                box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1) !important;
            }
            
            /* 卡片和容器优化 */
            .card, .panel, .register-card, .login-card, .auth-card,
            .modal, .popup, .dialog {
                padding: var(--tablet-spacing-xl) !important;
                border-radius: 20px !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
                transition: all 0.3s ease !important;
            }
            
            .card:hover, .panel:hover, .register-card:hover, .login-card:hover {
                transform: translateY(-4px) !important;
                box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2) !important;
            }

            /* 主页测试套件卡片横屏优化 */
            .test-suites-accordion {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)) !important;
                gap: 24px !important;
                margin-bottom: 32px !important;
            }

            .suite-accordion-item {
                width: 100% !important;
                max-width: none !important;
                margin-bottom: 0 !important;
            }

            .suite-header {
                padding: 24px !important;
                min-height: 120px !important;
            }

            .suite-main-info {
                flex-direction: column !important;
                gap: 16px !important;
                align-items: flex-start !important;
            }

            .suite-actions {
                align-self: stretch !important;
                justify-content: space-between !important;
            }

            .suite-start-btn {
                flex: 1 !important;
                max-width: 200px !important;
                min-height: 56px !important;
                font-size: 16px !important;
                padding: 16px 24px !important;
            }

            .suite-expand-btn {
                width: 56px !important;
                height: 56px !important;
                flex-shrink: 0 !important;
            }

            /* 任务卡片优化 */
            .tasks-list {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
                gap: 16px !important;
                padding: 24px !important;
            }

            .task-accordion-item {
                margin-bottom: 0 !important;
            }

            .task-header {
                padding: 20px !important;
                min-height: 80px !important;
            }

            .task-basic-info {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 12px !important;
            }

            .task-icon {
                width: 56px !important;
                height: 56px !important;
                font-size: 28px !important;
            }

            /* 历史记录卡片优化 */
            .records-accordion {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
                gap: 20px !important;
            }

            .record-date-group {
                margin-bottom: 0 !important;
            }

            .date-records {
                display: grid !important;
                grid-template-columns: 1fr !important;
                gap: 12px !important;
            }

            /* 链接优化 */
            a {
                min-height: var(--touch-target-min) !important;
                padding: 12px 16px !important;
                display: inline-flex !important;
                align-items: center !important;
                border-radius: 8px !important;
                transition: all 0.3s ease !important;
            }
            
            a:hover {
                background: rgba(0, 122, 255, 0.1) !important;
                transform: scale(1.05) !important;
            }
            
            /* 列表项优化 */
            .list-item, .menu-item, li {
                min-height: var(--touch-target-large) !important;
                padding: var(--tablet-spacing-lg) var(--tablet-spacing-xl) !important;
                border-radius: 12px !important;
                transition: all 0.3s ease !important;
                margin: 8px 0 !important;
            }
            
            /* 文字大小优化 */
            h1 { font-size: 32px !important; line-height: 1.2 !important; }
            h2 { font-size: 28px !important; line-height: 1.3 !important; }
            h3 { font-size: 24px !important; line-height: 1.3 !important; }
            h4 { font-size: 20px !important; line-height: 1.4 !important; }
            h5 { font-size: 18px !important; line-height: 1.4 !important; }
            h6 { font-size: 16px !important; line-height: 1.4 !important; }
            
            p, span, div, label {
                font-size: 16px !important;
                line-height: 1.5 !important;
            }
            
            .small-text, small {
                font-size: 14px !important;
            }
            
            /* 表单标签优化 */
            label {
                font-weight: 600 !important;
                margin-bottom: 12px !important;
                display: block !important;
            }
            
            /* 复选框和单选按钮优化 */
            input[type="checkbox"], input[type="radio"] {
                width: 24px !important;
                height: 24px !important;
                margin-right: 16px !important;
            }
            
            /* 滚动条优化 */
            ::-webkit-scrollbar {
                width: 14px !important;
                height: 14px !important;
            }
            
            ::-webkit-scrollbar-thumb {
                border-radius: 7px !important;
                background: rgba(0, 0, 0, 0.3) !important;
                min-height: 40px !important;
            }
            
            ::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.5) !important;
            }
        }
        
        /* 触摸设备专用优化 */
        @media (pointer: coarse) {
            *:focus {
                outline: 3px solid #007AFF !important;
                outline-offset: 2px !important;
            }
            
            /* 减少悬停效果依赖 */
            *:hover {
                transition-duration: 0.1s !important;
            }
        }
        
        /* 平板端横屏优化 */
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
            .container, .main-content {
                max-width: 90% !important;
                margin: 0 auto !important;
            }
        }
        
        /* 平板端竖屏优化 */
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
            .container, .main-content {
                max-width: 95% !important;
                margin: 0 auto !important;
                padding: var(--tablet-spacing-lg) !important;
            }
        }
        
        /* 特殊优化类 */
        .tablet-optimized {
            transform-origin: center !important;
        }
        
        .tablet-optimized:active {
            transform: scale(0.98) !important;
        }
        
        .touch-feedback {
            position: relative !important;
            overflow: hidden !important;
        }
        
        .touch-feedback::after {
            content: '' !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            width: 0 !important;
            height: 0 !important;
            background: rgba(255, 255, 255, 0.5) !important;
            border-radius: 50% !important;
            transform: translate(-50%, -50%) !important;
            transition: width 0.3s ease, height 0.3s ease !important;
            pointer-events: none !important;
        }
        
        .touch-feedback:active::after {
            width: 200px !important;
            height: 200px !important;
        }
        </style>
    `;
    
    // 2. 注入样式到页面
    if (!document.getElementById('tablet-optimization-styles')) {
        document.head.insertAdjacentHTML('beforeend', optimizationCSS);
        console.log('✅ 平板端优化CSS样式已注入');
    } else {
        console.log('⚠️ 平板端优化样式已存在，跳过注入');
    }
    
    // 3. 优化选中的元素
    function optimizeSelectedElement() {
        const selectedElement = document.querySelector('[data-selected="true"]') || 
                               document.querySelector('.selected') ||
                               document.querySelector('[aria-selected="true"]');
        
        if (selectedElement) {
            // 应用特殊样式
            selectedElement.style.cssText += `
                min-height: 72px !important;
                padding: 24px 32px !important;
                border-radius: 16px !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
                position: relative !important;
                overflow: hidden !important;
            `;
            
            // 添加优化类
            selectedElement.classList.add('tablet-optimized', 'touch-feedback');
            
            // 添加触摸事件监听器
            selectedElement.addEventListener('touchstart', function(e) {
                this.style.transform = 'scale(0.98)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
            }, { passive: true });
            
            selectedElement.addEventListener('touchend', function(e) {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
            }, { passive: true });
            
            // 添加鼠标事件（用于测试）
            selectedElement.addEventListener('mousedown', function(e) {
                this.style.transform = 'scale(0.98)';
            });
            
            selectedElement.addEventListener('mouseup', function(e) {
                this.style.transform = 'scale(1)';
            });
            
            console.log('✅ 选中元素已优化:', selectedElement);
            return selectedElement;
        } else {
            console.log('⚠️ 未找到选中的元素');
            return null;
        }
    }
    
    // 4. 批量优化所有交互元素
    function optimizeAllInteractiveElements() {
        const selectors = [
            'button', '.btn', '[role="button"]',
            'input[type="text"]', 'input[type="email"]', 'input[type="password"]',
            'input[type="tel"]', 'input[type="number"]', 'input[type="search"]',
            'textarea', 'select',
            'a[href]',
            '.card', '.panel', '.list-item', '.menu-item'
        ];
        
        const elements = document.querySelectorAll(selectors.join(', '));
        let optimizedCount = 0;
        
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            
            // 只优化可见元素
            if (rect.width > 0 && rect.height > 0) {
                // 确保最小触摸目标
                if (rect.width < 44 || rect.height < 44) {
                    element.style.minWidth = '44px';
                    element.style.minHeight = '44px';
                }
                
                // 添加优化类
                element.classList.add('tablet-optimized');
                
                // 为按钮和链接添加触摸反馈
                if (element.matches('button, .btn, [role="button"], a[href]')) {
                    element.classList.add('touch-feedback');
                }
                
                optimizedCount++;
            }
        });
        
        console.log(`✅ 已优化 ${optimizedCount} 个交互元素`);
        return optimizedCount;
    }
    
    // 5. 设备检测和自适应
    function detectAndApplyOptimizations() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const isTablet = screenWidth >= 768 && screenWidth <= 1024;
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        const isLandscape = screenWidth > screenHeight;
        
        // 移除之前的类
        document.body.classList.remove('tablet-device', 'touch-device', 'landscape-mode', 'portrait-mode');
        
        if (isTablet) {
            document.body.classList.add('tablet-device');
            console.log('📱 检测到平板设备尺寸');
        }
        
        if (isTouchDevice) {
            document.body.classList.add('touch-device');
            console.log('👆 检测到触摸设备');
        }
        
        if (isLandscape) {
            document.body.classList.add('landscape-mode');
            console.log('🔄 横屏模式');
        } else {
            document.body.classList.add('portrait-mode');
            console.log('📱 竖屏模式');
        }
        
        // 设置CSS变量
        document.documentElement.style.setProperty('--screen-width', screenWidth + 'px');
        document.documentElement.style.setProperty('--screen-height', screenHeight + 'px');
        
        return {
            isTablet,
            isTouchDevice,
            isLandscape,
            screenWidth,
            screenHeight
        };
    }
    
    // 6. 执行优化
    console.log('🔍 检测设备类型...');
    const deviceInfo = detectAndApplyOptimizations();
    
    console.log('🎯 优化选中元素...');
    const selectedElement = optimizeSelectedElement();
    
    console.log('⚡ 批量优化交互元素...');
    const optimizedCount = optimizeAllInteractiveElements();
    
    // 7. 监听窗口大小变化
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            console.log('📐 窗口大小变化，重新检测设备类型...');
            detectAndApplyOptimizations();
        }, 250);
    });
    
    // 8. 添加调试信息
    console.log('📊 优化统计:', {
        设备信息: deviceInfo,
        选中元素: selectedElement ? '已优化' : '未找到',
        优化元素数量: optimizedCount,
        样式注入: '成功'
    });
    
    // 9. 返回优化结果
    const result = {
        success: true,
        deviceInfo,
        selectedElement,
        optimizedCount,
        message: '平板端优化完成！'
    };
    
    console.log('🎉 平板端优化完成！');
    console.log('💡 提示：在平板设备上测试触摸交互效果');
    
    return result;
})();
