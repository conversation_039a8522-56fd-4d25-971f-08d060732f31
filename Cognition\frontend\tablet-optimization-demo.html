<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平板端选择区域优化演示</title>
    <style>
        :root {
            --primary: oklch(0.6487 0.1538 150.3071);
            --primary-foreground: oklch(1.0000 0 0);
            --background: oklch(0.9824 0.0013 286.3757);
            --foreground: oklch(0.3211 0 0);
            --card: oklch(1.0000 0 0);
            --border: oklch(0.8699 0 0);
            --muted: oklch(0.8828 0.0285 98.1033);
            --muted-foreground: oklch(0.5382 0 0);
            --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
            --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
            --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background);
            color: var(--foreground);
            padding: 20px;
            line-height: 1.6;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-section {
            background: var(--card);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border);
        }

        .demo-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 16px;
        }

        .demo-subtitle {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 24px;
            color: var(--foreground);
        }

        /* PDQ5 选项样式演示 */
        .option-item {
            display: flex;
            align-items: center;
            gap: 24px;
            padding: 24px 32px;
            background: var(--card);
            border: 2px solid var(--border);
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 64px;
            position: relative;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .option-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary);
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 0;
        }

        .option-item:hover {
            background: var(--muted);
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .option-item:active::before {
            opacity: 0.1;
        }

        .option-item.selected {
            background: oklch(from var(--primary) l c h / 0.1);
            border-color: var(--primary);
            box-shadow: var(--shadow-xl);
        }

        .option-item > * {
            position: relative;
            z-index: 1;
        }

        .option-radio {
            width: 32px;
            height: 32px;
            border: 3px solid oklch(0.88 0.005 240);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .option-item.selected .option-radio {
            border-color: var(--primary);
            background: var(--primary);
            box-shadow: 0 0 0 4px oklch(from var(--primary) l c h / 0.2);
        }

        .radio-dot {
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            transform: scale(0);
            transition: transform 0.2s ease;
        }

        .option-item.selected .radio-dot {
            transform: scale(1);
        }

        .option-content {
            flex: 1;
        }

        .option-main {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .option-value {
            width: 40px;
            height: 40px;
            background: var(--muted);
            color: var(--foreground);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 16px;
            flex-shrink: 0;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option-item.selected .option-value {
            background: var(--primary);
            color: white;
            border-color: white;
            transform: scale(1.1);
        }

        .option-text {
            font-weight: 600;
            color: var(--foreground);
            font-size: 22px;
            line-height: 1.3;
            flex: 1;
        }

        .option-item.selected .option-text {
            color: var(--primary);
            font-weight: 700;
        }

        /* 数字选择按钮演示 */
        .number-options {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 24px;
            margin: 24px 0;
            flex-wrap: wrap;
            padding: 16px 0;
        }

        .number-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: white;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            user-select: none;
            border: 4px solid transparent;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .number-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary);
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
            border-radius: 50%;
        }

        .number-button:hover {
            transform: scale(1.15);
            box-shadow: var(--shadow-lg);
        }

        .number-button:active::before {
            opacity: 0.2;
            transform: scale(1);
        }

        .number-button.selected {
            background: var(--primary);
            color: white;
            border-color: white;
            transform: scale(1.2);
            box-shadow: var(--shadow-xl);
        }

        .number-button span {
            position: relative;
            z-index: 1;
        }

        /* 手风琴演示 */
        .accordion-item {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .accordion-item:hover {
            box-shadow: var(--shadow-lg);
            border-color: oklch(from var(--primary) l c h / 0.3);
        }

        .accordion-header {
            padding: 32px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 96px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .accordion-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .accordion-header:hover {
            background: var(--muted);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .accordion-header:active::before {
            opacity: 0.05;
        }

        .accordion-header > * {
            position: relative;
            z-index: 1;
        }

        .accordion-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--foreground);
        }

        .accordion-toggle {
            width: 64px;
            height: 64px;
            background: var(--muted);
            color: var(--muted-foreground);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .accordion-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary);
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
            border-radius: 50%;
        }

        .accordion-toggle:hover {
            background: var(--primary);
            color: var(--primary-foreground);
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        .accordion-toggle:active::before {
            opacity: 0.2;
            transform: scale(1);
        }

        .demo-note {
            background: var(--muted);
            padding: 16px;
            border-radius: 12px;
            margin-top: 24px;
            border-left: 4px solid var(--primary);
        }

        .demo-note p {
            margin: 0;
            color: var(--muted-foreground);
            font-size: 14px;
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            .demo-title {
                font-size: 28px;
            }
            
            .number-button {
                width: 90px;
                height: 90px;
                font-size: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; font-size: 32px; margin-bottom: 32px; color: var(--primary);">
            🎯 平板端选择区域优化演示
        </h1>

        <div class="demo-section">
            <h2 class="demo-title">PDQ5测试选项优化</h2>
            <p class="demo-subtitle">增大触摸目标，增强视觉反馈，优化间距布局</p>
            
            <div class="option-item" onclick="toggleSelection(this)">
                <div class="option-radio">
                    <div class="radio-dot"></div>
                </div>
                <div class="option-content">
                    <div class="option-main">
                        <span class="option-value">0</span>
                        <span class="option-text">从不</span>
                    </div>
                </div>
            </div>

            <div class="option-item" onclick="toggleSelection(this)">
                <div class="option-radio">
                    <div class="radio-dot"></div>
                </div>
                <div class="option-content">
                    <div class="option-main">
                        <span class="option-value">1</span>
                        <span class="option-text">偶尔</span>
                    </div>
                </div>
            </div>

            <div class="option-item" onclick="toggleSelection(this)">
                <div class="option-radio">
                    <div class="radio-dot"></div>
                </div>
                <div class="option-content">
                    <div class="option-main">
                        <span class="option-value">2</span>
                        <span class="option-text">有时</span>
                    </div>
                </div>
            </div>

            <div class="demo-note">
                <p>✨ 优化特点：64px最小高度，32px单选按钮，增强的触摸反馈动画</p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">数字选择按钮优化</h2>
            <p class="demo-subtitle">适合情感测试的大型数字按钮，支持快速选择</p>
            
            <div class="number-options">
                <div class="number-button" onclick="selectNumber(this)">
                    <span>1</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>2</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>3</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>4</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>5</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>6</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>7</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>8</span>
                </div>
                <div class="number-button" onclick="selectNumber(this)">
                    <span>9</span>
                </div>
            </div>

            <div class="demo-note">
                <p>✨ 优化特点：80px×80px按钮大小，涟漪触摸效果，平板端可达90px×90px</p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">手风琴展开优化</h2>
            <p class="demo-subtitle">任务列表的展开交互，增大触摸区域和按钮</p>
            
            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <div class="accordion-title">🧠 认知能力综合测试</div>
                    <button class="accordion-toggle">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <div class="accordion-title">📝 PDQ-5 帕金森病问卷</div>
                    <button class="accordion-toggle">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="demo-note">
                <p>✨ 优化特点：96px最小高度，64px展开按钮，增强的悬停和触摸效果</p>
            </div>
        </div>
    </div>

    <script>
        function toggleSelection(element) {
            // 清除同级元素的选中状态
            const siblings = element.parentNode.querySelectorAll('.option-item');
            siblings.forEach(sibling => sibling.classList.remove('selected'));
            
            // 选中当前元素
            element.classList.add('selected');
        }

        function selectNumber(element) {
            // 清除同级元素的选中状态
            const siblings = element.parentNode.querySelectorAll('.number-button');
            siblings.forEach(sibling => sibling.classList.remove('selected'));
            
            // 选中当前元素
            element.classList.add('selected');
        }

        function toggleAccordion(element) {
            const toggle = element.querySelector('.accordion-toggle');
            const isExpanded = toggle.style.transform === 'rotate(180deg)';
            
            if (isExpanded) {
                toggle.style.transform = 'rotate(0deg)';
            } else {
                toggle.style.transform = 'rotate(180deg)';
            }
        }
    </script>
</body>
</html>
