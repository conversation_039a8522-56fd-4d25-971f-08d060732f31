<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>主页卡片平板优化演示 - Cognition</title>
    <style>
        /* 基础样式 */
        :root {
            --primary: #22c55e;
            --primary-foreground: #ffffff;
            --primary-hover: #16a34a;
            --secondary: #8b5cf6;
            --card: #ffffff;
            --border: #e5e7eb;
            --muted: #f3f4f6;
            --muted-foreground: #6b7280;
            --foreground: #111827;
            --background: #f9fafb;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background);
            color: var(--foreground);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: var(--card);
            border-radius: 16px;
            border: 1px solid var(--border);
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--primary);
        }
        
        .header p {
            color: var(--muted-foreground);
            font-size: 16px;
        }
        
        .optimization-controls {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 44px;
        }
        
        .btn-primary {
            background: var(--primary);
            color: var(--primary-foreground);
        }
        
        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: var(--muted);
            color: var(--foreground);
            border: 1px solid var(--border);
        }
        
        .btn-secondary:hover {
            background: var(--border);
        }
        
        /* 模拟主页结构 */
        .main-content {
            padding: 24px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
            color: var(--foreground);
        }
        
        /* 测试套件样式 */
        .test-suites-accordion {
            margin-bottom: 40px;
        }
        
        .suite-accordion-item {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .suite-header {
            padding: 20px;
            cursor: pointer;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .suite-main-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .suite-title-section {
            flex: 1;
        }
        
        .suite-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .suite-meta {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        .suite-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .suite-start-btn {
            background: var(--primary);
            color: var(--primary-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .suite-expand-btn {
            background: var(--muted);
            color: var(--muted-foreground);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .suite-progress {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .suite-progress-bar {
            flex: 1;
            height: 6px;
            background: var(--muted);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .suite-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
            transition: width 0.8s ease;
        }
        
        .suite-progress-text {
            font-size: 12px;
            font-weight: 600;
            color: var(--primary);
            min-width: 60px;
        }
        
        /* 历史记录样式 */
        .records-accordion {
            margin-bottom: 40px;
        }
        
        .record-date-group {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        
        .date-header {
            padding: 16px;
            background: var(--muted);
            cursor: pointer;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .date-info h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .date-count {
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-completed {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-partial {
            background: #fef3c7;
            color: #92400e;
        }
        
        /* 任务列表样式 */
        .tasks-list {
            padding: 16px;
        }
        
        .task-accordion-item {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
        }
        
        .task-header {
            padding: 16px;
            cursor: pointer;
            min-height: 64px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .task-basic-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .task-icon {
            width: 40px;
            height: 40px;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--muted);
            border-radius: 8px;
        }
        
        .task-main h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .task-brief {
            display: flex;
            gap: 8px;
            font-size: 12px;
            color: var(--muted-foreground);
        }
        
        .device-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            color: var(--muted-foreground);
            z-index: 1000;
        }
        
        .optimization-status {
            margin-top: 20px;
            padding: 16px;
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 8px;
            text-align: center;
        }
        
        .status-active {
            border-color: var(--primary);
            background: #f0fdf4;
        }
        
        .status-inactive {
            border-color: var(--muted);
            background: var(--muted);
        }
    </style>
</head>
<body>
    <div class="device-info" id="deviceInfo">
        设备信息加载中...
    </div>
    
    <div class="container">
        <div class="header">
            <h1>🏠 主页卡片平板优化演示</h1>
            <p>专为平板横屏模式优化的卡片布局展示</p>
        </div>
        
        <div class="optimization-controls">
            <button class="btn btn-primary" onclick="optimizeNow()">🚀 立即优化</button>
            <button class="btn btn-secondary" onclick="resetLayout()">🔄 重置布局</button>
            <button class="btn btn-secondary" onclick="toggleDemo()">📱 切换演示模式</button>
        </div>
        
        <div class="optimization-status" id="optimizationStatus">
            <span>优化状态：等待中...</span>
        </div>
        
        <div class="main-content">
            <!-- 测试套件部分 -->
            <section>
                <h2 class="section-title">📋 认知测试套件</h2>
                <div class="test-suites-accordion">
                    <div class="suite-accordion-item">
                        <div class="suite-header">
                            <div class="suite-main-info">
                                <div class="suite-title-section">
                                    <h3 class="suite-name">综合认知评估套件</h3>
                                    <div class="suite-meta">
                                        <span>编号：CS-001</span>
                                        <span>创建日期：2024-01-15</span>
                                    </div>
                                </div>
                                <div class="suite-actions">
                                    <button class="suite-start-btn">开始测试</button>
                                    <button class="suite-expand-btn">▼</button>
                                </div>
                            </div>
                            <div class="suite-progress">
                                <div class="suite-progress-bar">
                                    <div class="suite-progress-fill" style="width: 65%"></div>
                                </div>
                                <span class="suite-progress-text">65% 已完成</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="suite-accordion-item">
                        <div class="suite-header">
                            <div class="suite-main-info">
                                <div class="suite-title-section">
                                    <h3 class="suite-name">记忆力专项测试</h3>
                                    <div class="suite-meta">
                                        <span>编号：MS-002</span>
                                        <span>创建日期：2024-01-20</span>
                                    </div>
                                </div>
                                <div class="suite-actions">
                                    <button class="suite-start-btn">开始测试</button>
                                    <button class="suite-expand-btn">▼</button>
                                </div>
                            </div>
                            <div class="suite-progress">
                                <div class="suite-progress-bar">
                                    <div class="suite-progress-fill" style="width: 30%"></div>
                                </div>
                                <span class="suite-progress-text">30% 已完成</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="suite-accordion-item">
                        <div class="suite-header">
                            <div class="suite-main-info">
                                <div class="suite-title-section">
                                    <h3 class="suite-name">注意力集中度测试</h3>
                                    <div class="suite-meta">
                                        <span>编号：AT-003</span>
                                        <span>创建日期：2024-01-25</span>
                                    </div>
                                </div>
                                <div class="suite-actions">
                                    <button class="suite-start-btn">开始测试</button>
                                    <button class="suite-expand-btn">▼</button>
                                </div>
                            </div>
                            <div class="suite-progress">
                                <div class="suite-progress-bar">
                                    <div class="suite-progress-fill" style="width: 85%"></div>
                                </div>
                                <span class="suite-progress-text">85% 已完成</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 历史记录部分 -->
            <section>
                <h2 class="section-title">📊 历史测试记录</h2>
                <div class="records-accordion">
                    <div class="record-date-group">
                        <div class="date-header">
                            <div class="date-info">
                                <h3>2024年1月30日</h3>
                                <span class="date-count">3 次测试</span>
                            </div>
                            <span class="status-indicator status-completed">已完成</span>
                        </div>
                    </div>
                    
                    <div class="record-date-group">
                        <div class="date-header">
                            <div class="date-info">
                                <h3>2024年1月28日</h3>
                                <span class="date-count">2 次测试</span>
                            </div>
                            <span class="status-indicator status-partial">部分完成</span>
                        </div>
                    </div>
                    
                    <div class="record-date-group">
                        <div class="date-header">
                            <div class="date-info">
                                <h3>2024年1月25日</h3>
                                <span class="date-count">4 次测试</span>
                            </div>
                            <span class="status-indicator status-completed">已完成</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
    
    <!-- 加载优化脚本 -->
    <script src="./home-page-tablet-optimization.js"></script>
    
    <script>
        // 更新设备信息显示
        function updateDeviceInfo() {
            const info = {
                width: window.innerWidth,
                height: window.innerHeight,
                orientation: window.innerWidth > window.innerHeight ? '横屏' : '竖屏',
                isTablet: window.innerWidth >= 768 && window.innerWidth <= 1024,
                isTouchDevice: 'ontouchstart' in window
            };
            
            document.getElementById('deviceInfo').innerHTML = `
                屏幕: ${info.width}×${info.height}<br>
                方向: ${info.orientation}<br>
                平板: ${info.isTablet ? '是' : '否'}<br>
                触摸: ${info.isTouchDevice ? '支持' : '不支持'}
            `;
            
            return info;
        }
        
        // 更新优化状态
        function updateOptimizationStatus(active) {
            const statusEl = document.getElementById('optimizationStatus');
            if (active) {
                statusEl.className = 'optimization-status status-active';
                statusEl.innerHTML = '<span>✅ 优化状态：已激活</span>';
            } else {
                statusEl.className = 'optimization-status status-inactive';
                statusEl.innerHTML = '<span>⚠️ 优化状态：未激活</span>';
            }
        }
        
        // 立即优化
        function optimizeNow() {
            if (window.HomePageTabletOptimizer) {
                const result = window.HomePageTabletOptimizer.optimize();
                updateOptimizationStatus(result);
                console.log('手动触发优化:', result);
            } else {
                console.error('优化器未加载');
            }
        }
        
        // 重置布局
        function resetLayout() {
            // 移除优化CSS
            const optimizationStyle = document.getElementById('home-page-tablet-optimization');
            if (optimizationStyle) {
                optimizationStyle.remove();
            }
            
            // 重置内联样式
            document.querySelectorAll('[style]').forEach(el => {
                el.removeAttribute('style');
            });
            
            updateOptimizationStatus(false);
            console.log('布局已重置');
        }
        
        // 切换演示模式
        function toggleDemo() {
            const body = document.body;
            if (body.style.transform) {
                body.style.transform = '';
                body.style.transformOrigin = '';
            } else {
                // 模拟平板横屏视图
                body.style.transform = 'scale(0.8)';
                body.style.transformOrigin = 'top left';
            }
        }
        
        // 监听窗口变化
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', updateDeviceInfo);
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            
            // 检查优化状态
            setTimeout(() => {
                const hasOptimization = document.getElementById('home-page-tablet-optimization');
                updateOptimizationStatus(!!hasOptimization);
            }, 1000);
        });
    </script>
</body>
</html>
